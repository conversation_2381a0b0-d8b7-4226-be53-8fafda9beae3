#-----------------------------------------------------------
# Vivado v2021.1 (64-bit)
# SW Build 3247384 on Thu Jun 10 19:36:33 MDT 2021
# IP Build 3246043 on Fri Jun 11 00:30:35 MDT 2021
# Start of session at: Sat Jul 26 20:55:26 2025
# Process ID: 13672
# Current directory: f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex
# Command line: vivado.exe -notrace -source f:/project/FPGA/test/mk7100_aurora_test/mk7100_aurora_test.gen/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0_ex.tcl
# Log file: f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/vivado.log
# Journal file: f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex\vivado.jou
#-----------------------------------------------------------
start_gui
source f:/project/FPGA/test/mk7100_aurora_test/mk7100_aurora_test.gen/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0_ex.tcl -notrace
INFO: [open_example_project] Creating new example project...
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'G:/Devtools/xilinx_2021.1/Vivado/2021.1/data/ip'.
INFO: [open_example_project] Importing original IP ...
INFO: [open_example_project] Generating the example project IP ...
INFO: [IP_Flow 19-1706] Not generating 'Instantiation Template' target for IP 'aurora_64b66b_0'. Target already exists and is up to date.
INFO: [IP_Flow 19-1706] Not generating 'Synthesis' target for IP 'aurora_64b66b_0'. Target already exists and is up to date.
INFO: [IP_Flow 19-1706] Not generating 'Simulation' target for IP 'aurora_64b66b_0'. Target already exists and is up to date.
INFO: [IP_Flow 19-1706] Not generating 'Shared Logic Files' target for IP 'aurora_64b66b_0'. Target already exists and is up to date.
INFO: [open_example_project] Adding example synthesis HDL files ...
INFO: [open_example_project] Adding example XDC files ...
INFO: [open_example_project] Adding simulation HDL files ...
INFO: [open_example_project] Sourcing example extension scripts ...
INFO: [open_example_project] Sourcing extension script: exdes_pre_bdgen.tcl
INFO: [open_example_project] Sourcing extension script: exdes_pre_bdgen_partner.tcl
INFO: [open_example_project] Sourcing extension script: aurora_64b66b_0/tcl/gen_axi4strm_reg_slice.tcl
WARNING: [IP_Flow 19-4832] The IP name 'aurora_64b66b_0_reg_slice_0' you have specified is long. The Windows operating system has path length limitations. It is recommended you use shorter names to reduce the likelihood of issues.
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'aurora_64b66b_0_reg_slice_0'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'aurora_64b66b_0_reg_slice_0'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'aurora_64b66b_0_reg_slice_0'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'aurora_64b66b_0_reg_slice_0'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'aurora_64b66b_0_reg_slice_0'...
WARNING: [IP_Flow 19-4832] The IP name 'aurora_64b66b_0_reg_slice_2' you have specified is long. The Windows operating system has path length limitations. It is recommended you use shorter names to reduce the likelihood of issues.
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'aurora_64b66b_0_reg_slice_2'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'aurora_64b66b_0_reg_slice_2'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'aurora_64b66b_0_reg_slice_2'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'aurora_64b66b_0_reg_slice_2'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'aurora_64b66b_0_reg_slice_2'...
INFO: [open_example_project] Rebuilding all the top level IPs ...
INFO: [SIM-utils-72] Using boost library from 'G:/Devtools/xilinx_2021.1/Vivado/2021.1/tps/boost_1_72_0'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XSIM" (Xilinx Vivado Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/xsim/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "MODELSIM" (Mentor Graphics ModelSim Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/modelsim/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "QUESTA" (Mentor Graphics Questa Advanced Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/questa/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "IES" (Cadence Incisive Enterprise Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/ies/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XCELIUM" (Cadence Xcelium Parallel Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/xcelium/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "VCS" (Synopsys Verilog Compiler Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/vcs/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "RIVIERA" (Aldec Riviera-PRO Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/riviera/aurora_64b66b_0_reg_slice_2.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "ACTIVEHDL" (Aldec Active-HDL Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_2/activehdl/aurora_64b66b_0_reg_slice_2.sh'
INFO: [SIM-utils-72] Using boost library from 'G:/Devtools/xilinx_2021.1/Vivado/2021.1/tps/boost_1_72_0'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XSIM" (Xilinx Vivado Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/xsim/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "MODELSIM" (Mentor Graphics ModelSim Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/modelsim/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "QUESTA" (Mentor Graphics Questa Advanced Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/questa/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "IES" (Cadence Incisive Enterprise Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/ies/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XCELIUM" (Cadence Xcelium Parallel Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/xcelium/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "VCS" (Synopsys Verilog Compiler Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/vcs/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "RIVIERA" (Aldec Riviera-PRO Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/riviera/aurora_64b66b_0_reg_slice_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "ACTIVEHDL" (Aldec Active-HDL Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0_reg_slice_0/activehdl/aurora_64b66b_0_reg_slice_0.sh'
INFO: [SIM-utils-72] Using boost library from 'G:/Devtools/xilinx_2021.1/Vivado/2021.1/tps/boost_1_72_0'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XSIM" (Xilinx Vivado Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/xsim/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "MODELSIM" (Mentor Graphics ModelSim Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/modelsim/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "QUESTA" (Mentor Graphics Questa Advanced Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/questa/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "IES" (Cadence Incisive Enterprise Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/ies/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "XCELIUM" (Cadence Xcelium Parallel Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/xcelium/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "VCS" (Synopsys Verilog Compiler Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/vcs/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "RIVIERA" (Aldec Riviera-PRO Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/riviera/aurora_64b66b_0.sh'
INFO: [exportsim-Tcl-35] Exporting simulation files for "ACTIVEHDL" (Aldec Active-HDL Simulator)...
INFO: [exportsim-Tcl-29] Script generated: 'F:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.ip_user_files/sim_scripts/aurora_64b66b_0/activehdl/aurora_64b66b_0.sh'
INFO: [open_example_project] Open Example Project completed
update_compile_order -fileset sources_1

 //////////////////////////////////////////////////////////////////////////////
 // Project:  Aurora 64B/66B
 // Company:  Xilinx
 //
 //
 //
 // (c) Copyright 2008 - 2009 Xilinx, Inc. All rights reserved.
 //
 // This file contains confidential and proprietary information
 // of Xilinx, Inc. and is protected under U.S. and
 // international copyright and other intellectual property
 // laws.
 //
 // DISCLAIMER
 // This disclaimer is not a license and does not grant any
 // rights to the materials distributed herewith. Except as
 // otherwise provided in a valid license issued to you by
 // Xilinx, and to the maximum extent permitted by applicable
 // law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
 // WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
 // AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
 // BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
 // INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
 // (2) Xilinx shall not be liable (whether in contract or tort,
 // including negligence, or under any other theory of
 // liability) for any loss or damage of any kind or nature
 // related to, arising under or in connection with these
 // materials, including for any direct, or any indirect,
 // special, incidental, or consequential loss or damage
 // (including loss of data, profits, goodwill, or any type of
 // loss or damage suffered as a result of any action brought
 // by a third party) even if such damage or loss was
 // reasonably foreseeable or Xilinx had been advised of the
 // possibility of the same.
 //
 // CRITICAL APPLICATIONS
 // Xilinx products are not designed or intended to be fail-
 // safe, or for use in any application requiring fail-safe
 // performance, such as life-support or safety devices or
 // systems, Class III medical devices, nuclear facilities,
 // applications related to the deployment of airbags, or any
 // other applications that could lead to death, personal
 // injury, or severe property or environmental damage
 // (individually and collectively, "Critical
 // Applications"). Customer assumes the sole risk and
 // liability of any use of Xilinx products in Critical
 // Applications, subject only to applicable laws and
 // regulations governing limitations on product liability.
 //
 // THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
 // PART OF THIS FILE AT ALL TIMES.
 
 //
 //////////////////////////////////////////////////////////////////////////////
 //
 //  FRAME CHECK
 //
 //
 //
 //  Description: This module is a  pattern checker to test the Aurora
 //               designs in hardware. The frames generated by FRAME_GEN
 //               pass through the Aurora channel and arrive at the frame checker 
 //               through the RX User interface. Every time an error is found in
 //               the data recieved, the error count is incremented until it 
 //               reaches its max value.
 //////////////////////////////////////////////////////////////////////////////
 
 `timescale 1 ns / 10 ps
 `define DLY #1
 
(* DowngradeIPIdentifiedWarnings="yes" *)
 module aurora_64b66b_0_FRAME_CHECK
 (
     // User Interface
     RX_D,  
     RX_REM,
     RX_SOF_N,
     RX_EOF_N,
     RX_SRC_RDY_N,  
     DATA_ERR_COUNT,
 
 
 
     // System Interface
     CHANNEL_UP,
     USER_CLK,       
     RESET
   
 );
 //*********************** Parameter Declarations************************
     parameter            AURORA_LANES    = 2;
     parameter            LANE_DATA_WIDTH = (AURORA_LANES*64);
     parameter            REM_BUS         = 4;
     parameter            DATA_WIDTH      = 8;
 
 //***********************************Port Declarations*******************************
     //PDU Interface
     input     [0:LANE_DATA_WIDTH-1]    RX_D;
     input     [0:REM_BUS-1]            RX_REM;
     input                              RX_SOF_N; 
     input                              RX_EOF_N; 
     input                              RX_SRC_RDY_N; 
     
     //System Interface
     input                              CHANNEL_UP; 
     input                              USER_CLK; 
     input                              RESET;  
     output    [0:DATA_WIDTH-1]         DATA_ERR_COUNT;
 
 //***************************Internal Register Declarations*************************** 
 
     //PDU interface signals
     reg       [0:DATA_WIDTH-1]         DATA_ERR_COUNT; 
     reg       [0:15]                   pdu_lfsr_r;
     reg       [LANE_DATA_WIDTH-1:0]    pdu_cmp_data_r;
     reg       [0:LANE_DATA_WIDTH-1]    RX_D_R;  
     reg                                pdu_data_valid_r;
     reg                                pdu_in_frame_r;
     reg       [0:AURORA_LANES-1]                   pdu_err_detected_c;
     wire       [0:LANE_DATA_WIDTH-1]    pdu_cmp_data_r1;
     wire      [0:AURORA_LANES-1]                   data_err_c; 

 (* shift_extract = "{no}"*)    reg       [0:LANE_DATA_WIDTH-1]    RX_D_R2;
 (* shift_extract = "{no}"*)    reg       [0:REM_BUS-1]            RX_REM_R2;
 (* shift_extract = "{no}"*)    reg       [0:REM_BUS-1]            RX_REM_R3;
 (* shift_extract = "{no}"*)    reg                                RX_SOF_N_R2;
 (* shift_extract = "{no}"*)    reg                                RX_EOF_N_R2;
 (* shift_extract = "{no}"*)    reg                                RX_SRC_RDY_N_R2; 
 
     wire                               pdu_in_frame_c;
     wire      [0:LANE_DATA_WIDTH-1]    pdu_lfsr_concat_w;  
     wire                               pdu_data_valid_c;
 
     wire                               reset_i; 
     wire                               RESET_ii; 


 //*********************************Main Body of Code**********************************
 
   assign reset_i = RESET || (!CHANNEL_UP);
   assign resetUFC = reset_i; 

    assign RESET_ii = RESET ; 

  /*****************************PDU Data Genetration & Checking**********************/


 
     //Generate the PDU data using LFSR for data comparision
     always @ (posedge USER_CLK)
     if(reset_i)
       pdu_lfsr_r  <=  `DLY  16'hD5E6;
     else if(pdu_data_valid_c)
       pdu_lfsr_r  <=  `DLY  {!{pdu_lfsr_r[3]^pdu_lfsr_r[12]^pdu_lfsr_r[14]^pdu_lfsr_r[15]}, 
                            pdu_lfsr_r[0:14]};
 
     assign pdu_lfsr_concat_w = {AURORA_LANES*4{pdu_lfsr_r}};

    always @ (posedge USER_CLK)
    begin
       RX_D_R2         <= `DLY RX_D;
       RX_REM_R2       <= `DLY RX_REM; 
       RX_REM_R3       <= `DLY RX_REM;
       RX_SOF_N_R2     <= `DLY RX_SOF_N; 
       RX_EOF_N_R2     <= `DLY RX_EOF_N; 
       RX_SRC_RDY_N_R2 <= `DLY RX_SRC_RDY_N; 
    end

 
     //______________________________ Capture incoming data ___________________________    
 
     //PDU data is valid when RX_SRC_RDY_N_R2 is asserted
     assign pdu_data_valid_c    =   pdu_in_frame_c && !RX_SRC_RDY_N_R2;
 
     //PDU data is in a frame if it is a single cycle frame or a multi_cycle frame has started
     assign  pdu_in_frame_c  =   pdu_in_frame_r  ||  (!RX_SRC_RDY_N_R2 && !RX_SOF_N_R2);
 
     //Start a multicycle PDU frame when a frame starts without ending on the same cycle. End 
     //the frame when an RX_EOF_N_R2 is detected
     always @(posedge USER_CLK)
     if(RESET_ii)
       pdu_in_frame_r  <=  `DLY    1'b0;
     else if(!pdu_in_frame_r && !RX_SOF_N_R2 && !RX_SRC_RDY_N_R2 && RX_EOF_N_R2)
       pdu_in_frame_r  <=  `DLY    1'b1;
     else if(pdu_in_frame_r && !RX_SRC_RDY_N_R2 && !RX_EOF_N_R2)
       pdu_in_frame_r  <=  `DLY    1'b0;
 
     //Register and decode the RX_D_R2 data with RX_REM_R2 bus
     always @ (posedge USER_CLK)
     begin 	       
       if((!RX_EOF_N_R2) && (!RX_SRC_RDY_N_R2))
       begin	 
         case(RX_REM_R2)
4'd0 : RX_D_R <=  `DLY RX_D_R2;
4'd1 : RX_D_R <=  `DLY {RX_D_R2[0:7], 120'b0};
4'd2 : RX_D_R <=  `DLY {RX_D_R2[0:15], 112'b0};
4'd3 : RX_D_R <=  `DLY {RX_D_R2[0:23], 104'b0};
4'd4 : RX_D_R <=  `DLY {RX_D_R2[0:31], 96'b0};
4'd5 : RX_D_R <=  `DLY {RX_D_R2[0:39], 88'b0};
4'd6 : RX_D_R <=  `DLY {RX_D_R2[0:47], 80'b0};
4'd7 : RX_D_R <=  `DLY {RX_D_R2[0:55], 72'b0};
4'd8 : RX_D_R <=  `DLY {RX_D_R2[0:63], 64'b0};
4'd9 : RX_D_R <=  `DLY {RX_D_R2[0:71], 56'b0};
4'd10 : RX_D_R <=  `DLY {RX_D_R2[0:79], 48'b0};
4'd11 : RX_D_R <=  `DLY {RX_D_R2[0:87], 40'b0};
4'd12 : RX_D_R <=  `DLY {RX_D_R2[0:95], 32'b0};
4'd13 : RX_D_R <=  `DLY {RX_D_R2[0:103], 24'b0};
4'd14 : RX_D_R <=  `DLY {RX_D_R2[0:111], 16'b0};
4'd15 : RX_D_R <=  `DLY {RX_D_R2[0:119], 8'b0};
           default : RX_D_R  <=  `DLY RX_D_R2; 		 
 	endcase 	 
       end  
       else if(!RX_SRC_RDY_N_R2)
         RX_D_R          <=  `DLY    RX_D_R2;
     end 
 
     //Calculate the expected PDU data 
     always @ (posedge USER_CLK)
     begin
       if(reset_i)
         pdu_cmp_data_r <= `DLY {AURORA_LANES*4{16'hD5E6}};
       else if(pdu_data_valid_c)
       begin		 
         case(RX_REM_R3)
4'd0 : pdu_cmp_data_r <= `DLY pdu_lfsr_concat_w;
4'd1 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:7], 120'b0};
4'd2 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:15], 112'b0};
4'd3 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:23], 104'b0};
4'd4 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:31], 96'b0};
4'd5 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:39], 88'b0};
4'd6 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:47], 80'b0};
4'd7 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:55], 72'b0};
4'd8 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:63], 64'b0};
4'd9 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:71], 56'b0};
4'd10 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:79], 48'b0};
4'd11 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:87], 40'b0};
4'd12 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:95], 32'b0};
4'd13 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:103], 24'b0};
4'd14 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:111], 16'b0};
4'd15 : pdu_cmp_data_r <=  `DLY {pdu_lfsr_concat_w[0:119], 8'b0};
           default : pdu_cmp_data_r <=  `DLY pdu_lfsr_concat_w; 		 
 	endcase 	 
       end
     end
 
     //PDU Data in the pdu_cmp_data_r register is valid only if it was valid when captured and had no error
     always @(posedge USER_CLK)
       if(reset_i)   
         pdu_data_valid_r    <=  `DLY    1'b0;
       else
         pdu_data_valid_r    <=  `DLY    pdu_data_valid_c && !pdu_err_detected_c;

     //
     assign pdu_cmp_data_r1 = {
       pdu_cmp_data_r[127], 
       pdu_cmp_data_r[126], 
       pdu_cmp_data_r[125], 
       pdu_cmp_data_r[124], 
       pdu_cmp_data_r[123], 
       pdu_cmp_data_r[122], 
       pdu_cmp_data_r[121], 
       pdu_cmp_data_r[120], 
       pdu_cmp_data_r[119], 
       pdu_cmp_data_r[118], 
       pdu_cmp_data_r[117], 
       pdu_cmp_data_r[116], 
       pdu_cmp_data_r[115], 
       pdu_cmp_data_r[114], 
       pdu_cmp_data_r[113], 
       pdu_cmp_data_r[112], 
       pdu_cmp_data_r[111], 
       pdu_cmp_data_r[110], 
       pdu_cmp_data_r[109], 
       pdu_cmp_data_r[108], 
       pdu_cmp_data_r[107], 
       pdu_cmp_data_r[106], 
       pdu_cmp_data_r[105], 
       pdu_cmp_data_r[104], 
       pdu_cmp_data_r[103], 
       pdu_cmp_data_r[102], 
       pdu_cmp_data_r[101], 
       pdu_cmp_data_r[100], 
       pdu_cmp_data_r[99], 
       pdu_cmp_data_r[98], 
       pdu_cmp_data_r[97], 
       pdu_cmp_data_r[96], 
       pdu_cmp_data_r[95], 
       pdu_cmp_data_r[94], 
       pdu_cmp_data_r[93], 
       pdu_cmp_data_r[92], 
       pdu_cmp_data_r[91], 
       pdu_cmp_data_r[90], 
       pdu_cmp_data_r[89], 
       pdu_cmp_data_r[88], 
       pdu_cmp_data_r[87], 
       pdu_cmp_data_r[86], 
       pdu_cmp_data_r[85], 
       pdu_cmp_data_r[84], 
       pdu_cmp_data_r[83], 
       pdu_cmp_data_r[82], 
       pdu_cmp_data_r[81], 
       pdu_cmp_data_r[80], 
       pdu_cmp_data_r[79], 
       pdu_cmp_data_r[78], 
       pdu_cmp_data_r[77], 
       pdu_cmp_data_r[76], 
       pdu_cmp_data_r[75], 
       pdu_cmp_data_r[74], 
       pdu_cmp_data_r[73], 
       pdu_cmp_data_r[72], 
       pdu_cmp_data_r[71], 
       pdu_cmp_data_r[70], 
       pdu_cmp_data_r[69], 
       pdu_cmp_data_r[68], 
       pdu_cmp_data_r[67], 
       pdu_cmp_data_r[66], 
       pdu_cmp_data_r[65], 
       pdu_cmp_data_r[64], 
       pdu_cmp_data_r[63], 
       pdu_cmp_data_r[62], 
       pdu_cmp_data_r[61], 
       pdu_cmp_data_r[60], 
       pdu_cmp_data_r[59], 
       pdu_cmp_data_r[58], 
       pdu_cmp_data_r[57], 
       pdu_cmp_data_r[56], 
       pdu_cmp_data_r[55], 
       pdu_cmp_data_r[54], 
       pdu_cmp_data_r[53], 
       pdu_cmp_data_r[52], 
       pdu_cmp_data_r[51], 
       pdu_cmp_data_r[50], 
       pdu_cmp_data_r[49], 
       pdu_cmp_data_r[48], 
       pdu_cmp_data_r[47], 
       pdu_cmp_data_r[46], 
       pdu_cmp_data_r[45], 
       pdu_cmp_data_r[44], 
       pdu_cmp_data_r[43], 
       pdu_cmp_data_r[42], 
       pdu_cmp_data_r[41], 
       pdu_cmp_data_r[40], 
       pdu_cmp_data_r[39], 
       pdu_cmp_data_r[38], 
       pdu_cmp_data_r[37], 
       pdu_cmp_data_r[36], 
       pdu_cmp_data_r[35], 
       pdu_cmp_data_r[34], 
       pdu_cmp_data_r[33], 
       pdu_cmp_data_r[32], 
       pdu_cmp_data_r[31], 
       pdu_cmp_data_r[30], 
       pdu_cmp_data_r[29], 
       pdu_cmp_data_r[28], 
       pdu_cmp_data_r[27], 
       pdu_cmp_data_r[26], 
       pdu_cmp_data_r[25], 
       pdu_cmp_data_r[24], 
       pdu_cmp_data_r[23], 
       pdu_cmp_data_r[22], 
       pdu_cmp_data_r[21], 
       pdu_cmp_data_r[20], 
       pdu_cmp_data_r[19], 
       pdu_cmp_data_r[18], 
       pdu_cmp_data_r[17], 
       pdu_cmp_data_r[16], 
       pdu_cmp_data_r[15], 
       pdu_cmp_data_r[14], 
       pdu_cmp_data_r[13], 
       pdu_cmp_data_r[12], 
       pdu_cmp_data_r[11], 
       pdu_cmp_data_r[10], 
       pdu_cmp_data_r[9], 
       pdu_cmp_data_r[8], 
       pdu_cmp_data_r[7], 
       pdu_cmp_data_r[6], 
       pdu_cmp_data_r[5], 
       pdu_cmp_data_r[4], 
       pdu_cmp_data_r[3], 
       pdu_cmp_data_r[2], 
       pdu_cmp_data_r[1], 
       pdu_cmp_data_r[0] 
     };

  assign data_err_c[0] = ( pdu_data_valid_r && (RX_D_R[0:63] != pdu_cmp_data_r1[0:63]));      
  assign data_err_c[1] = ( pdu_data_valid_r && (RX_D_R[64:127] != pdu_cmp_data_r1[64:127]));      

 
     //An error is detected when LFSR generated PDU data from the pdu_cmp_data_r register, 
     //does not match valid data from the RX_D port
 always @(posedge USER_CLK)
     pdu_err_detected_c    <=  `DLY    data_err_c;

 
     //Compare the incoming PDU data with calculated expected PDU data.
     //Increment the PDU ERR COUNTER if mismatch occurs
     //Stop the PDU ERR COUNTER once it reaches its max value
     always @ (posedge USER_CLK)
     begin	       
       if(RESET_ii)
         DATA_ERR_COUNT <= `DLY 8'b0;
       else if(!CHANNEL_UP)
         DATA_ERR_COUNT <= `DLY 8'b0;
       else if(&DATA_ERR_COUNT)
         DATA_ERR_COUNT <= `DLY DATA_ERR_COUNT;
       else if(|pdu_err_detected_c)
         DATA_ERR_COUNT <= `DLY DATA_ERR_COUNT + 1; 
       end 
 
 endmodule           

 ##################################################################################
 ##
 ## Project:  Aurora 64B/66B
 ## Company:  Xilinx
 ##
 ##
 ##
 ## (c) Copyright 2012 - 2013 Xilinx, Inc. All rights reserved.
 ##
 ## This file contains confidential and proprietary information
 ## of Xilinx, Inc. and is protected under U.S. and
 ## international copyright and other intellectual property
 ## laws.
 ##
 ## DISCLAIMER
 ## This disclaimer is not a license and does not grant any
 ## rights to the materials distributed herewith. Except as
 ## otherwise provided in a valid license issued to you by
 ## Xilinx, and to the maximum extent permitted by applicable
 ## law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
 ## WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
 ## AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
 ## BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
 ## INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
 ## (2) Xilinx shall not be liable (whether in contract or tort,
 ## including negligence, or under any other theory of
 ## liability) for any loss or damage of any kind or nature
 ## related to, arising under or in connection with these
 ## materials, including for any direct, or any indirect,
 ## special, incidental, or consequential loss or damage
 ## (including loss of data, profits, goodwill, or any type of
 ## loss or damage suffered as a result of any action brought
 ## by a third party) even if such damage or loss was
 ## reasonably foreseeable or Xilinx had been advised of the
 ## possibility of the same.
 ##
 ## CRITICAL APPLICATIONS
 ## Xilinx products are not designed or intended to be fail-
 ## safe, or for use in any application requiring fail-safe
 ## performance, such as life-support or safety devices or
 ## systems, Class III medical devices, nuclear facilities,
 ## applications related to the deployment of airbags, or any
 ## other applications that could lead to death, personal
 ## injury, or severe property or environmental damage
 ## (individually and collectively, "Critical
 ## Applications"). Customer assumes the sole risk and
 ## liability of any use of Xilinx products in Critical
 ## Applications, subject only to applicable laws and
 ## regulations governing limitations on product liability.
 ##
 ## THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
 ## PART OF THIS FILE AT ALL TIMES.
 
 ##
 #################################################################################
 
 ##
 ##  aurora_64b66b_0
 ##
 ##
 ##  Description: This is the design constraints file for a 2 lane Aurora
 ##               core. 
 ##               It contains all the clocks generated by the core
 ##
 ##
################################################################################ 
 
################################################################################
			# below commented constraints are kept as is to maintain the backward compatibility structure.
			## set_false_path -from [get_clocks -of_objects [get_ports init_clk_out]] -to [get_clocks -of_objects [get_pins inst/clock_module_i/mmcm_adv_inst/CLKOUT0]]
			## set_false_path -from [get_clocks -of_objects [get_pins inst/clock_module_i/mmcm_adv_inst/CLKOUT0]] -to [get_clocks -of_objects [get_ports init_clk_out]]
			## set_false_path -from [get_clocks -of_objects [get_ports init_clk_out]] -to [get_clocks -of_objects [get_pins inst/clock_module_i/mmcm_adv_inst/CLKOUT1]]
			## set_false_path -from [get_clocks -of_objects [get_pins inst/clock_module_i/mmcm_adv_inst/CLKOUT1]] -to [get_clocks -of_objects [get_ports init_clk_out]]
			## set_false_path -from [get_clocks -of_objects [get_ports init_clk_out]] -to [get_clocks -of_objects [get_pins -filter {REF_PIN_NAME=~*RXOUTCLK} -of_objects [get_cells -hierarchical -filter {NAME =~ *inst*aurora_64b66b_0_core_i*aurora_64b66b_0_wrapper_i*aurora_64b66b_0_multi_gt_i*aurora_64b66b_0_gtx_inst/gtxe2_i*}]]]
			## set_false_path -from [get_clocks -of_objects [get_pins -filter {REF_PIN_NAME=~*RXOUTCLK} -of_objects [get_cells -hierarchical -filter {NAME =~ *inst*aurora_64b66b_0_core_i*aurora_64b66b_0_wrapper_i*aurora_64b66b_0_multi_gt_i*aurora_64b66b_0_gtx_inst/gtxe2_i*}]]] -to [get_clocks -of_objects [get_ports init_clk_out]]
			## set_false_path -from [get_clocks -of_objects [get_pins -filter {REF_PIN_NAME=~*RXOUTCLK} -of_objects [get_cells -hierarchical -filter {NAME =~ *inst*aurora_64b66b_0_core_i*aurora_64b66b_0_wrapper_i*aurora_64b66b_0_multi_gt_i*aurora_64b66b_0_gtx_inst/gtxe2_i*}]]] -to [get_clocks -of_objects [get_ports user_clk_out]]
			## set_false_path -from [get_clocks -of_objects [get_ports user_clk_out]] -to [get_clocks -of_objects [get_pins -filter {REF_PIN_NAME=~*RXOUTCLK} -of_objects [get_cells -hierarchical -filter {NAME =~ *inst*aurora_64b66b_0_core_i*aurora_64b66b_0_wrapper_i*aurora_64b66b_0_multi_gt_i*aurora_64b66b_0_gtx_inst/gtxe2_i*}]]]
 
 
 
 
################################################################################
################################################################################



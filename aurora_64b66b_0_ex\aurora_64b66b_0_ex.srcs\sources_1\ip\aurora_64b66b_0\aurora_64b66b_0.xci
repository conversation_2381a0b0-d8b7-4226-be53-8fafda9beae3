<?xml version="1.0" encoding="UTF-8"?>
<spirit:design xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>xci</spirit:library>
  <spirit:name>unknown</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:componentInstances>
    <spirit:componentInstance>
      <spirit:instanceName>aurora_64b66b_0</spirit:instanceName>
      <spirit:componentRef spirit:vendor="xilinx.com" spirit:library="ip" spirit:name="aurora_64b66b" spirit:version="12.0"/>
      <spirit:configurableElementValues>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.ADDR_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.DATA_WIDTH">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_BRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_RRESP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_WSTRB">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ADDR_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ARUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.AWUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.BUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.DATA_WIDTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_BRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_CACHE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_LOCK">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_PROT">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_QOS">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_REGION">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_RRESP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_WSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.MAX_BURST_LENGTH">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.NUM_READ_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.NUM_READ_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.NUM_WRITE_OUTSTANDING">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.NUM_WRITE_THREADS">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.PROTOCOL">AXI4LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.READ_WRITE_MODE">READ_WRITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.RUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.RUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.SUPPORTS_NARROW_BURST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.WUSER_BITS_PER_BYTE">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.WUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK1.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK1.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK2.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK2.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK3.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK3.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK4.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK4.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK5.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DIFF_REFCLK5.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DRP_CLK_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DRP_CLK_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DRP_CLK_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DRP_CLK_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_DRP_CLK_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD10_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD11_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD12_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD1_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD2_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD3_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD4_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD5_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD6_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD7_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD8_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLCLK_QUAD9_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD10_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD11_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD12_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD1_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD2_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD3_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD4_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD5_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD6_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD7_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD8_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_QPLLREFCLK_QUAD9_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK1_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK2_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK3_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK4_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_REFCLK5_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RESET.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.GT_RXUSRCLK_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_CLK_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_DIFF_CLK.CAN_DEBUG">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.INIT_DIFF_CLK.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.LINK_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TKEEP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TLAST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TDATA_NUM_BYTES">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK1_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK2_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK3_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK4_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.REFCLK5_IN.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RESET2FC.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RESET2FG.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RESET_PB.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE0.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE1.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE10.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE11.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE12.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE13.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE14.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE15.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE2.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE3.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE4.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE5.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE6.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE7.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE8.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_LANE9.RX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_RESET_PB.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RX_SYS_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYNC_CLK_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.SYS_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE0.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE1.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE10.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE11.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE12.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE13.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE14.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE15.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE2.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE3.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE4.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE5.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE6.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE7.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE8.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.ADDITIONAL_QUAD_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.CHNL_NUMBER">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.GT_DIRECTION">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.MASTERCLK_SRC">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_LANE9.TX_SETTINGS">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_OUT_CLK.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_RESET_PB.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.TX_SYS_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TKEEP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TLAST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TDATA_NUM_BYTES">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TKEEP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TLAST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TDATA_NUM_BYTES">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_1.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_1.FREQ_HZ">156250000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_1.PARENT_ID">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_1.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_2.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_2.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_2.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_2.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_2.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_CLK_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TKEEP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TLAST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TDATA_NUM_BYTES">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TKEEP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TLAST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TREADY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TDATA_NUM_BYTES">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TKEEP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TLAST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TDATA_NUM_BYTES">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TKEEP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TLAST">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TREADY">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TDATA_NUM_BYTES">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TUSER_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_DOCCPORT_ENABLE">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK2_LOC_N">BL7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK2_LOC_P">BL8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK2_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK3_LOC_N">BL7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK3_LOC_P">BL8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK3_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK4_LOC_N">BL7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK4_LOC_P">BL8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK4_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK5_LOC_N">BL7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK5_LOC_P">BL8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK5_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK_LOC_N">BL7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK_LOC_P">BL8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_REFCLK_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_START_LANE">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_START_QUAD">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_active_transceiverquads">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PLL_TYPE">CPLL</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.RX_MASTER_CHANNEL">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.SINGLEEND_GTREFCLK">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.SINGLEEND_INITCLK">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.TX_MASTER_CHANNEL">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.USDRPADDR_WIDTH">8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aurora_lanes">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_column_used">left</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpll_fbdiv">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpll_fbdiv_45">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpll_refclk_div">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_example_simulation">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_clock_1">GTXQ1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_clock_2">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_clock_3">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_clock_4">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_clock_5">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_1">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_10">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_11">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_12">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_13">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_14">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_15">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_16">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_17">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_18">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_19">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_2">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_20">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_21">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_22">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_23">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_24">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_25">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_26">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_27">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_28">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_29">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_3">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_30">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_31">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_32">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_33">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_34">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_35">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_36">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_37">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_38">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_39">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_4">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_40">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_41">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_42">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_43">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_44">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_45">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_46">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_47">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_48">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_5">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_6">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_7">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_8">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_loc_9">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gt_type">gtx</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gtwiz_out">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_init_clk">50.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_lane_width">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_line_rate">5000.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_nfc">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_nfc_mode">IMM</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_no_gts_quad1">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_no_gts_quad2">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_no_gts_quad3">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_no_gts_quad4">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_no_gts_quad5">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_qpll">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_qpll_fbdiv_ratio">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_qpll_refclk_div">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_refclk_frequency">156250.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_remwidht">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rxoutdiv">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_simplex">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_simplex_mode">TX</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_stream">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_txoutdiv">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ucolumn_used">right</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ufc">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_use_byteswap">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_use_chipscope">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_user_interface">axi4_stream</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_user_k">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_xdevice">xc7z100</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_xpackage">ffg900</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_xspeedgrade">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.cc_refclk_frequency">156.250</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.channel_enable">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.component_name">aurora_64b66b_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.crc_mode">CRC32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dataflow_config">Duplex</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dmonitoroutval">7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.drp_freq">100.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.drp_mode">AXI4_LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.flow_mode">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.gtquadcnt">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ins_loss_nyq">20</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.interface_mode">Framing</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.is_7series">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.is_8series">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.is_board">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.is_versal">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rx_coupling">AC</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rx_eq_mode">AUTO</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rx_ppm_offset">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rx_termination">PROGRAMMABLE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rx_termination_prog_value">800</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.supportlevel">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.transceivercontrol">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.CHANNEL_ENABLE">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_AURORA_LANES">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_COLUMN_USED">left</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_DOCCPORT_ENABLE">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_EXAMPLE_SIMULATION">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GTWIZ_OUT">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_CLOCK_1">GTXQ1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_CLOCK_2">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_CLOCK_3">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_CLOCK_4">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_CLOCK_5">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_1">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_10">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_11">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_12">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_13">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_14">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_15">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_16">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_17">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_18">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_19">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_2">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_20">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_21">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_22">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_23">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_24">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_25">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_26">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_27">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_28">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_29">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_3">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_30">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_31">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_32">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_33">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_34">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_35">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_36">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_37">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_38">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_39">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_4">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_40">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_41">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_42">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_43">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_44">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_45">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_46">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_47">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_48">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_5">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_6">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_7">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_8">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_LOC_9">X</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_GT_TYPE">gtx</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_INIT_CLK">50.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_LINE_RATE">5</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_PLL_TYPE">LCPLL</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK2_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK3_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK4_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK5_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK_FREQUENCY">156.250</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK_FREQUENCY_ACTUAL">156.250</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK_FREQUENCY_VERSAL">156.250</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_REFCLK_SOURCE">none</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_START_LANE">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_START_QUAD">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_UCOLUMN_USED">right</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_USER_K">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_USE_BYTESWAP">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_USE_CHIPSCOPE">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.C_active_transceiverquads">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Component_Name">aurora_64b66b_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DRP_FREQ">100.0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.INS_LOSS_NYQ">20</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RX_COUPLING">AC</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RX_EQ_MODE">AUTO</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RX_PPM_OFFSET">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RX_TERMINATION">PROGRAMMABLE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RX_TERMINATION_PROG_VALUE">800</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.SINGLEEND_GTREFCLK">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.SINGLEEND_INITCLK">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.SupportLevel">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.TransceiverControl">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.crc_mode">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.dataflow_config">Duplex</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.drp_mode">AXI4_LITE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.flow_mode">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.interface_mode">Framing</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.ARCHITECTURE">zynq</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BASE_BOARD_PART"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BOARD_CONNECTIONS"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.DEVICE">xc7z100</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PACKAGE">ffg900</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PREFHDL">VERILOG</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SILICON_REVISION"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SIMULATOR_LANGUAGE">MIXED</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SPEEDGRADE">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.STATIC_POWER"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.TEMPERATURE_GRADE"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_CUSTOMIZATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_GENERATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPCONTEXT">IP_Flow</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPREVISION">6</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.MANAGED">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.OUTPUTDIR">../../../../aurora_64b66b_0_ex.gen/sources_1/ip/aurora_64b66b_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SELECTEDSIMMODEL"/>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SHAREDDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SWVERSION">2021.1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SYNTHESISFLOW">OUT_OF_CONTEXT</spirit:configurableElementValue>
      </spirit:configurableElementValues>
      <spirit:vendorExtensions>
        <xilinx:componentInstanceExtensions>
          <xilinx:configElementInfos>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_0.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_1.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_10.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_11.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_12.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_13.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_14.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_15.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_2.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_3.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_4.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_5.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_6.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_7.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_8.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ADDR_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ARUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.AWUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.BUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.DATA_WIDTH" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_BRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_BURST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_CACHE" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_LOCK" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_PROT" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_QOS" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_REGION" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_RRESP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.HAS_WSTRB" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.ID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.PROTOCOL" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.RUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_DRP_IF_9.WUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TKEEP" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TREADY" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.NFC_S_AXIS_TX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TREADY" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_M_AXIS_RX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TKEEP" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TREADY" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.UFC_S_AXIS_TX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TKEEP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TLAST" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TREADY" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_M_AXIS_RX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TKEEP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TLAST" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_DATA_S_AXIS_TX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TKEEP" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TREADY" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_M_AXIS_RX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TKEEP" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TREADY" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.USER_K_S_AXIS_TX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_AURORA_LANES" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_GT_CLOCK_1" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_GT_LOC_1" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_GT_LOC_5" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_GT_LOC_6" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.C_LINE_RATE" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.SINGLEEND_GTREFCLK" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.SINGLEEND_INITCLK" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.SupportLevel" xilinx:valueSource="user"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.crc_mode" xilinx:valueSource="user"/>
          </xilinx:configElementInfos>
          <xilinx:boundaryDescriptionInfo>
            <xilinx:boundaryDescription xilinx:boundaryDescriptionJSON="{&quot;ip_boundary&quot;:{&quot;ports&quot;:{&quot;channel_up&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;crc_pass_fail_n&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;crc_valid&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;drp_clk_in&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;gt_pll_lock&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;gt_qpllclk_quad2_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;gt_qpllrefclk_quad2_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;gt_reset_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;gt_rxcdrovrden_in&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;hard_err&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is
_vector&quot;:&quot;false&quot;}],&quot;init_clk&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;lane_up&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;link_reset_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;loopback&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axi_rx_tdata&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;127&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axi_rx_tkeep&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;15&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axi_rx_tlast&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;m_axi_rx_tvalid&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;mmcm_not_locked_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pma_init&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;po
wer_down&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;refclk1_in&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;reset_pb&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;rxn&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;rxp&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_araddr&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_araddr_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_arready&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_arready_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_arvalid&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_arvalid_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;p
hysical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_awaddr&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_awaddr_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_awready&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_awready_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_awvalid&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_awvalid_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_bready&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_bready_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_bresp&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_bresp_lane1&quot;:[{&quot;direction&quot;:&quot;o
ut&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_bvalid&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_bvalid_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_rdata&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_rdata_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_rready&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_rready_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_rresp&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_rresp_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_rvalid&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_rvalid_lane1&quot;:[{&quot;direction&quot;:
&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_tx_tdata&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;127&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_tx_tkeep&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;15&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_tx_tlast&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_tx_tready&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_tx_tvalid&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_wdata&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_wdata_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_wready&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_wready_lane1&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_wstrb&quot;:[{&quot;direction&quot;:&quot;in
&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_wstrb_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axi_wvalid&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axi_wvalid_lane1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;soft_err&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;sync_clk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;sys_reset_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;tx_out_clk&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;txn&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;txp&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;user_clk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;
:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}]},&quot;interfaces&quot;:{&quot;AXILITE_DRP_IF_0&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:aximm:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:aximm_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ADDR_WIDTH&quot;:[{&quot;value&quot;:&quot;32&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ARUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;AWUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;BUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:
&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;DATA_WIDTH&quot;:[{&quot;value&quot;:&quot;32&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_BRESP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_BURST&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_CACHE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips
_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_LOCK&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_PROT&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_QOS&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_REGION&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_RRESP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_WSTRB&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;
default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;MAX_BURST_LENGTH&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_READ_OUTSTANDING&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_READ_THREADS&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;ge
nerated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_WRITE_OUTSTANDING&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_WRITE_THREADS&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PROTOCOL&quot;:[{&quot;value&quot;:&quot;AXI4LITE&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;READ_WRITE_MODE&quot;:[{&quot;value&quot;:&quot;READ_WRITE&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;
,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;RUSER_BITS_PER_BYTE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;RUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;SUPPORTS_NARROW_BURST&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;WUSER_BITS_PER_BYTE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;WUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:fa
lse}]},&quot;port_maps&quot;:{&quot;ARADDR&quot;:[{&quot;physical_name&quot;:&quot;s_axi_araddr&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ARREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_arready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ARVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_arvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWADDR&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awaddr&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:
&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BRESP&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bresp&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rdata&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RRESP&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rresp&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wdata&quot;,&quot;physi
cal_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WSTRB&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wstrb&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;3&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;AXILITE_DRP_IF_1&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:aximm:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:aximm_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ADDR_WIDTH&quot;:[{&quot;value&quot;:&quot;32&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ARUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;u
sage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;AWUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;BUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;DATA_WIDTH&quot;:[{&quot;value&quot;:&quot;32&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HA
S_BRESP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_BURST&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_CACHE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_LOCK&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_PROT&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_QOS&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_typ
e&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_REGION&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_RRESP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_WSTRB&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_st
atic_object&quot;:false}],&quot;MAX_BURST_LENGTH&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_READ_OUTSTANDING&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_READ_THREADS&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_WRITE_OUTSTANDING&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;NUM_WRITE_THREADS&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:
&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PROTOCOL&quot;:[{&quot;value&quot;:&quot;AXI4LITE&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;READ_WRITE_MODE&quot;:[{&quot;value&quot;:&quot;READ_WRITE&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;RUSER_BITS_PER_BYTE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;RUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;SUPPORTS_NARROW_BURST&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;val
ue_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;WUSER_BITS_PER_BYTE&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;WUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;ARADDR&quot;:[{&quot;physical_name&quot;:&quot;s_axi_araddr_lane1&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ARREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_arready_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ARVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_arvalid_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWADDR
&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awaddr_lane1&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awready_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;AWVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_awvalid_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bready_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BRESP&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bresp_lane1&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_bvalid_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rdata_lane1&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_r
ight&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rready_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RRESP&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rresp_lane1&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_rvalid_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wdata_lane1&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wready_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WSTRB&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wstrb_lane1&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;3&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WV
ALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_wvalid_lane1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;CORE_CONTROL&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:display_aurora:core_control_in:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:display_aurora:core_control_in_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;GT_RXCDROVRDEN_IN&quot;:[{&quot;physical_name&quot;:&quot;gt_rxcdrovrden_in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;LOOPBACK&quot;:[{&quot;physical_name&quot;:&quot;loopback&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;PLL_NOT_LOCKED&quot;:[{&quot;physical_name&quot;:&quot;mmcm_not_locked&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;Power_down&quot;:[{&quot;physical_name&quot;:&quot;power_down&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;CORE_STATUS&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:displ
ay_aurora:core_status_out:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:display_aurora:core_status_out_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;BUFG_GT_CLR_OUT&quot;:[{&quot;physical_name&quot;:&quot;bufg_gt_clr_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;CHANNEL_UP&quot;:[{&quot;physical_name&quot;:&quot;channel_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;CRC_PASS_FAIL&quot;:[{&quot;physical_name&quot;:&quot;crc_pass_fail_n&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;CRC_VALID&quot;:[{&quot;physical_name&quot;:&quot;crc_valid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;GT_PLL_LOCK&quot;:[{&quot;physical_name&quot;:&quot;gt_pll_lock&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;GT_TO_COMMON_QPLLRESET_OUT&quot;:[{&quot;physical_name&quot;:&quot;gt_to_common_qpllreset_out&quot;,&quot;physica
l_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;HARD_ERR&quot;:[{&quot;physical_name&quot;:&quot;hard_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;LANE_UP&quot;:[{&quot;physical_name&quot;:&quot;lane_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;PLL_NOT_LOCKED_OUT&quot;:[{&quot;physical_name&quot;:&quot;mmcm_not_locked_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RX_CHANNEL_UP&quot;:[{&quot;physical_name&quot;:&quot;rx_channel_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RX_HARD_ERR&quot;:[{&quot;physical_name&quot;:&quot;rx_hard_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RX_LANE_UP&quot;:[{&quot;physical_name&quot;:&quot;rx_lane_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_u
sed&quot;:&quot;none&quot;}],&quot;RX_SOFT_ERR&quot;:[{&quot;physical_name&quot;:&quot;rx_soft_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;SOFT_ERR&quot;:[{&quot;physical_name&quot;:&quot;soft_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TX_CHANNEL_UP&quot;:[{&quot;physical_name&quot;:&quot;tx_channel_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TX_HARD_ERR&quot;:[{&quot;physical_name&quot;:&quot;tx_hard_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TX_LANE_UP&quot;:[{&quot;physical_name&quot;:&quot;tx_lane_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TX_SOFT_ERR&quot;:[{&quot;physical_name&quot;:&quot;tx_soft_err&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;GT_SERIAL_RX&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:display_aurora:GT_Serial_Transceiver_Pin
s_RX:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:display_aurora:GT_Serial_Transceiver_Pins_RX_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;RXN&quot;:[{&quot;physical_name&quot;:&quot;rxn&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;RXP&quot;:[{&quot;physical_name&quot;:&quot;rxp&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;GT_SERIAL_TX&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:display_aurora:GT_Serial_Transceiver_Pins_TX:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:display_aurora:GT_Serial_Transceiver_Pins_TX_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;TXN&quot;:[{&quot;physical_name&quot;:&quot;txn&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TXP&quot;:[{&quot;physical_name&quot;:&quot;txp&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;1&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;1&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;USER_DATA_M_AXIS_RX&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:axis:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:inte
rface:axis_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TKEEP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TLAST&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TREADY&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TSTRB&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;val
ue_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;LAYERED_METADATA&quot;:[{&quot;value&quot;:&quot;undef&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TDATA_NUM_BYTES&quot;:[{&quot;value&quot;:&quot;16&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TDEST_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;
:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;TDATA&quot;:[{&quot;physical_name&quot;:&quot;m_axi_rx_tdata&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;127&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;127&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TKEEP&quot;:[{&quot;physical_name&quot;:&quot;m_axi_rx_tkeep&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;15&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;15&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TLAST&quot;:[{&quot;physical_name&quot;:&quot;m_axi_rx_tlast&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TVALID&quot;:[{&quot;physical_name&quot;:&quot;m_axi_rx_tvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;p
hysical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;USER_DATA_S_AXIS_TX&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:axis:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:axis_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TKEEP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TLAST&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TREADY&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value
_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;HAS_TSTRB&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;LAYERED_METADATA&quot;:[{&quot;value&quot;:&quot;undef&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TDATA_NUM_BYTES&quot;:[{&quot;value&quot;:&quot;16&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;gen
erated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TDEST_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;TUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;TDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axi_tx_tdata&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;127&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;127&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TKEEP&quot;:[{&quot;physical_name&quot;:&quot;s_axi_tx_tkeep&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;15&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;15&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TLAST&quot;:[{&quot;physical_name&quot;:&quot;s_axi_tx
_tlast&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axi_tx_tready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axi_tx_tvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;gt_drp_clk_in&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;CHANNEL_DRP_IF0:CHANNEL_DRP_IF1:CHANNEL_DRP_IF2:CHANNEL_DRP_IF3:CHANNEL_DRP_IF4:CHANNEL_DRP_IF5:CHANNEL_DRP_IF6:CHANNEL_DRP_IF7:CHANNEL_DRP_IF8:CHANNEL_DRP_IF9:CHANNEL_DRP_IF10:CHANNEL_DRP_IF11:CHANNEL_DRP_IF12:CHANNEL_DRP_IF13:CHANNEL_DRP_IF14:CHANNEL_DRP_IF15:GTCOMMON_DRP_IF0:GTCOMMON_DRP_IF1:GTCOMMON_DRP_IF2:GTCOMMON_DRP_IF3:GTCOMMON_DRP_IF4:GTCOMMON_DRP_IF5:GTCOMMON_DRP_IF6:GTCOMMON_DRP_IF7:GTCOMMON_DRP_IF8:
AXILITE_DRP_IF_0:AXILITE_DRP_IF_1:AXILITE_DRP_IF_2:AXILITE_DRP_IF_3:AXILITE_DRP_IF_4:AXILITE_DRP_IF_5:AXILITE_DRP_IF_6:AXILITE_DRP_IF_7:AXILITE_DRP_IF_8:AXILITE_DRP_IF_9:AXILITE_DRP_IF_10:AXILITE_DRP_IF_11:AXILITE_DRP_IF_12:AXILITE_DRP_IF_13:AXILITE_DRP_IF_14:AXILITE_DRP_IF_15&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;gt_reset_out&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_ob
ject&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;drp_clk_in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;gt_qpllclk_quad2_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;str
ing&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static
_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;gt_qpllclk_quad2_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;gt_qpllrefclk_quad2_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;
format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;gt_qpllrefclk_quad2_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;non
e&quot;}]}},&quot;gt_reset&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;pma_init&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;gt_reset_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false
}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;gt_reset_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;init_clk_in&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;GT0_DRP:GT1_DRP:GT2_DRP:GT3_DRP:GT4_DRP:GT5_DRP:GT6_DRP:GT7_DRP:GT8_DRP:GT9_DRP:GT10_DRP:GT11_DRP:GT12_DRP:GT13_DRP:GT14_DRP:GT15_DRP:AXILITE_DRP_IF_0:AXILITE_DRP_IF_1:AXILITE_DRP_IF_2:AXILITE_DRP_IF_3:AXILITE_DRP_IF_4:AXILITE_DRP_IF_5:AXILITE_DRP_IF_6:AXILITE_DRP_IF_7:AXILITE_DRP_IF_8:AXILITE_DRP_IF_9:AXILITE_DRP_IF_10:AXILITE_DRP_IF_11:AXILITE_DRP_IF_12:AXILITE_DRP_IF_13:AXILITE_DRP_IF_14:AXILITE_DRP_IF_15&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;
immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;gt_reset_out&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;
is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;init_clk&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;link_reset_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;link_reset_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right
&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;refclk1_in&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;va
lue_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;refclk1_in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;reset_pb&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:fal
se}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;reset_pb&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;sync_clk_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;
:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;sync_clk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;sys_reset_out&quot;:{&quot;vlnv&quot;:&quot;xil
inx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;sys_reset_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;tx_out_clk&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;
value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;res
olve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;tx_out_clk&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;user_clk_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;USER_DATA_S_AXIS_TX:USER_DATA_M_AXIS_RX:UFC_S_AXIS_TX:UFC_M_AXIS_RX:NFC_S_AXIS_TX:USER_K_S_AXIS_TX:USER_K_M_AXIS_RX&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;sys_reset_out:tx_sys_reset_out:rx_sys_reset_out:reset2fc:reset2fg&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:
&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;156250000&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;user_clk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logic
al_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}}}}}"/>
          </xilinx:boundaryDescriptionInfo>
        </xilinx:componentInstanceExtensions>
      </spirit:vendorExtensions>
    </spirit:componentInstance>
  </spirit:componentInstances>
</spirit:design>

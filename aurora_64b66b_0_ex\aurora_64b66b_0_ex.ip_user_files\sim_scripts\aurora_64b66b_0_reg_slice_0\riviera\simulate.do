onbreak {quit -force}
onerror {quit -force}

asim +access +r +m+aurora_64b66b_0_reg_slice_0 -L xpm -L axis_infrastructure_v1_1_0 -L axis_register_slice_v1_1_24 -L xil_defaultlib -L unisims_ver -L unimacro_ver -L secureip -O5 xil_defaultlib.aurora_64b66b_0_reg_slice_0 xil_defaultlib.glbl

set NumericStdNoWarnings 1
set StdArithNoWarnings 1

do {wave.do}

view wave
view structure

do {aurora_64b66b_0_reg_slice_0.udo}

run -all

endsim

quit -force

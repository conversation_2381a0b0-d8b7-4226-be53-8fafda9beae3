 ///////////////////////////////////////////////////////////////////////////////
 // Project:  Aurora 64B/66B
 // Company:  Xilinx  
 //
 //
 // (c) Copyright 2008 - 2009 Xilinx, Inc. All rights reserved.
 //
 // This file contains confidential and proprietary information
 // of Xilinx, Inc. and is protected under U.S. and
 // international copyright and other intellectual property
 // laws.
 //
 // DISCLAIMER
 // This disclaimer is not a license and does not grant any
 // rights to the materials distributed herewith. Except as
 // otherwise provided in a valid license issued to you by
 // Xilinx, and to the maximum extent permitted by applicable
 // law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
 // WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
 // AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
 // BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
 // INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
 // (2) Xilinx shall not be liable (whether in contract or tort,
 // including negligence, or under any other theory of
 // liability) for any loss or damage of any kind or nature
 // related to, arising under or in connection with these
 // materials, including for any direct, or any indirect,
 // special, incidental, or consequential loss or damage
 // (including loss of data, profits, goodwill, or any type of
 // loss or damage suffered as a result of any action brought
 // by a third party) even if such damage or loss was
 // reasonably foreseeable or Xilinx had been advised of the
 // possibility of the same.
 //
 // CRITICAL APPLICATIONS
 // Xilinx products are not designed or intended to be fail-
 // safe, or for use in any application requiring fail-safe
 // performance, such as life-support or safety devices or
 // systems, Class III medical devices, nuclear facilities,
 // applications related to the deployment of airbags, or any
 // other applications that could lead to death, personal
 // injury, or severe property or environmental damage
 // (individually and collectively, "Critical
 // Applications"). Customer assumes the sole risk and
 // liability of any use of Xilinx products in Critical
 // Applications, subject only to applicable laws and
 // regulations governing limitations on product liability.
 //
 // THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
 // PART OF THIS FILE AT ALL TIMES.
 
 //
 ////////////////////////////////////////////////////////////////////////////////
 //
 // Module SCRAMBLER_64B66B
 // Generated by Xilinx Aurora 64B66B
 
 `timescale 1ns / 10ps
 `define DLY #1
(* DowngradeIPIdentifiedWarnings="yes" *) 
 //***********************************Entity Declaration*******************************
 module aurora_64b66b_0_TX_CRC (
   DATA_DS,
   REM_DS,
   SOF_N_DS,
   EOF_N_DS,
   SRC_RDY_N_DS,
   DST_RDY_N_DS,
   DST_RDY_N_US,
   DATA_US,
   REM_US,
   SOF_N_US,
   EOF_N_US,
   SRC_RDY_N_US,
   RESET,
   CHANNEL_UP,
   CLK
 );
 
 parameter CRC_INIT  = 32'hFFFFFFFF;
 parameter DATA_WIDTH = 128;
 parameter REM_WIDTH = 4;
   //--*_DS refers to output signals from the block--//
   //--*_US refers to input signals to the block--//
   output [(DATA_WIDTH-1):0]   DATA_DS;
   output [(4-1):0]    REM_DS; 
   output      	  SOF_N_DS;
   output      	  EOF_N_DS;
   output      	  SRC_RDY_N_DS;
   input       	  DST_RDY_N_DS;
   output      	  DST_RDY_N_US;
   input [(DATA_WIDTH-1):0]    DATA_US;
   input [(4-1):0]     REM_US; 
   input       	  SOF_N_US;
   input       	  EOF_N_US;
   input       	  SRC_RDY_N_US;
   input       	  RESET;
   input       	  CHANNEL_UP;
   input       	  CLK;
   
   reg [(DATA_WIDTH-1):0]    DATA_DS;
   reg [(4-1):0]     REM_DS; 
   reg       	SOF_N_DS;
   reg       	EOF_N_DS;
   reg       	SRC_RDY_N_DS;
   
   //__Signal declaration for one-hot encoded FSM__//
   reg   idle_r;   //--Idle or power on state
   reg   sc_frame_r; //--single cycle frame state
   reg   sof_sc_r; //--sof state for single cycle frame when rem > 3
   reg   eof_sc_r; //--eof state for single cycle frame when rem > 3
   reg   sof_eof_r;  //--sof-eof state for single cycle frame when rem < 4
   reg   wait_r;   //--wait state
   reg   sof_ds_r; //-build sof state for non-single cycle frames
   reg   src_not_rdy_r;  //--state for input src not rdy
   reg   data_r;   //--data state
   reg   eof_ds_r; //--build eof state
   reg   crc_r;    //--append crc state for rem>3
   
   wire  idle_c;
   wire  sc_frame_c;
   wire  sof_sc_c;
   wire  eof_sc_c;
   wire  sof_eof_c;
   wire  wait_c;
   wire  sof_ds_c;
   wire  src_not_rdy_c;
   wire  data_c;
   wire  eof_ds_c;
   wire  crc_c;
   
   
   //__internal register declaration__//
   reg [(4-1):0] 	rem_in;   //--store input REM
   reg [(4-1):0] 	rem_in_i;   //--store input REM
   reg [(4-1):0]     rem_out;  //--build output REM
   reg     		EOF_N_US_r; //--EOF delayed by one cycle
   reg [1:0] 	count;    //--count to deassert dst-rdy-us after receiving EOF
   reg [(DATA_WIDTH-1):0]  	DATA_US_r;  //--data input pipeline-I
   reg [(DATA_WIDTH-1):0]  	DATA_US_2r; //--data input pipeine-II
   reg [(DATA_WIDTH/2 - 1):0]  	CRC_reg;    //--CRC output registered from CRC block
   
   //__internal wire declaration__//
   wire    		DST_RDY_N_US_i;
   wire    		ll_valid;   //__LocalLink valid
   wire    		CRC_DATAVALID1;   //__LocalLink valid
   wire [31:0] 	        CRC1;      //__CRC calculated
   wire [2:0]  	        CRC_DATAWIDTH1;  //__CRC datawidth
   wire [63:0] 	        CRC_DATA1;   //__data input to CRC block
   wire    		CRC_DATAVALID2;   //__LocalLink valid
   wire [31:0] 	        CRC2;      //__CRC calculated
   wire [2:0]  	        CRC_DATAWIDTH2;  //__CRC datawidth
   wire [63:0] 	        CRC_DATA2;   //__data input to CRC block
   wire [(DATA_WIDTH/2 - 1):0] 	final_CRC; 
   wire    		CRC_RST;    //__reset to CRC block
   wire    		reset_crc_block;    
  
  
   //____________Main code begins here________________//

  assign reset_crc_block = RESET || !CHANNEL_UP;
   
     //__Initialization & state assignment for FSM__//
   always @(posedge CLK)
     if (reset_crc_block)
     begin
       idle_r      	<= `DLY 1'b1;
       sc_frame_r    <= `DLY 1'b0;
       sof_sc_r    	<= `DLY 1'b0;
       eof_sc_r    	<= `DLY 1'b0;
       sof_eof_r   	<= `DLY 1'b0;
       wait_r      	<= `DLY 1'b0;
       src_not_rdy_r <= `DLY 1'b0;
       sof_ds_r    	<= `DLY 1'b0;
       data_r      	<= `DLY 1'b0;
       eof_ds_r    	<= `DLY 1'b0;
       crc_r     	<= `DLY 1'b0;
     end
     else if (!DST_RDY_N_DS)
     begin
       idle_r      	<= `DLY idle_c;
       sc_frame_r    <= `DLY sc_frame_c;
       sof_sc_r    	<= `DLY sof_sc_c;
       eof_sc_r    	<= `DLY eof_sc_c;
       sof_eof_r   	<= `DLY sof_eof_c;
       wait_r      	<= `DLY wait_c;
       src_not_rdy_r <= `DLY src_not_rdy_c;
       sof_ds_r    	<= `DLY sof_ds_c;
       data_r      	<= `DLY data_c;
       eof_ds_r    	<= `DLY eof_ds_c;
       crc_r     	<= `DLY crc_c;
     end
     
     //__Combinatorial logic for FSM__//
     
     //--stay in idle when not receiving any frames and
     //when current frame transmission is complete --//
   assign idle_c = (idle_r & (SOF_N_US || !ll_valid)) ||
           ((eof_ds_r || eof_sc_r || sof_eof_r) & (SOF_N_US || !ll_valid));
     //--reach this state when input is a single cycle frame
   assign sc_frame_c = ((idle_r || eof_sc_r || sof_eof_r || eof_ds_r) & 
                   !SOF_N_US & !EOF_N_US & ll_valid);
     //--reach this state when input is single cycle frame and rem < 4 so that
     //output is also a single cycle frame--//
   assign sof_eof_c  = sc_frame_r & (rem_in < 4'd8);
     //--reach this state when input is single cycle frame and rem > 3 so that
     //output is a two cycle frame--//
   assign sof_sc_c   = sc_frame_r & (rem_in > 4'd7);
     //--reach this state to build eof for the output two cycle frame--//
   assign eof_sc_c   =   sof_sc_r;
     //--reach this state when input is not single cycle frame and sof is asserted--//
   assign wait_c   = ((idle_r || eof_sc_r || sof_eof_r || eof_ds_r) & 
                   !SOF_N_US & EOF_N_US & ll_valid) || (wait_r & !ll_valid);
     //--this state builds the output SOF --//
   assign sof_ds_c   = wait_r & ll_valid;
     //--reach here when src-rdy is deasserted in betwwen a frame transmission--//
   assign src_not_rdy_c  = ((sof_ds_r || data_r) & SRC_RDY_N_US & EOF_N_US_r) ||
                   (src_not_rdy_r & SRC_RDY_N_US); 
     //--this is data state for frames--//
   assign data_c   = (sof_ds_r & EOF_N_US_r) ||
                 (data_r & EOF_N_US_r & !SRC_RDY_N_US) || 
           (src_not_rdy_r & !SRC_RDY_N_US & EOF_N_US_r);
     //--this is for adding extra cycle to insert CRC when input rem > 3--//
   assign crc_c    = ((sof_ds_r || data_r || (src_not_rdy_r & !SRC_RDY_N_US))
                  & (!EOF_N_US_r) & (rem_in > 4'd7));
     //--this is for eof buildup, also reached when input rem < 4--//        
   assign eof_ds_c   = ((sof_ds_r || data_r || (src_not_rdy_r & !SRC_RDY_N_US)) & (!EOF_N_US_r) & (rem_in < 4'd8)) ||
               crc_r;
               
     //__Store REM value and build output REM__//
   always @(posedge CLK)
     if (!EOF_N_US && ll_valid)
     begin
       rem_in  <= `DLY REM_US;     //--input REM storage
       rem_out <= `DLY (REM_US > 4'd6)? REM_US + 4'd9: REM_US + 4'd9;  //--output REM is input REM + 4 
     end
   
   always @ (posedge CLK)
      rem_in_i <= `DLY REM_US;
 
     //__input signals registered__//  
   always @(posedge CLK)
   if (ll_valid)
   begin
     EOF_N_US_r  <= `DLY EOF_N_US;
     DATA_US_r <= `DLY DATA_US;  //--data pipe-I
   end
 
   always @(posedge CLK)
   if (!DST_RDY_N_DS)
 //  if (ll_valid || (!EOF_N_US_r && !DST_RDY_N_DS))
   begin
       DATA_US_2r  <= `DLY DATA_US_r;    //--data pipe-II
   end
   
     //--deassert dst-rdy-us after EOF reception or when dst-rdy-ds is deasserted--//    
   assign DST_RDY_N_US = DST_RDY_N_US_i;
   assign DST_RDY_N_US_i = DST_RDY_N_DS || (|count); 
   
     //__deassert dst_rdy_n_us for 2 cycles after eof_n_us reception
       // to take care of crc insertion in DS-data__//
   always @(posedge CLK)
     if (reset_crc_block)
       count <= `DLY 2'b00;
     else if (!EOF_N_US && ll_valid)
       count <= `DLY 2'b10;  //--count 2 cycles explicitly when dst is ready__//
     else if (|count && !DST_RDY_N_DS)
       count <= `DLY count >> 1;
     //--Localink valid indicator--//
   assign ll_valid = !SRC_RDY_N_US && !DST_RDY_N_US_i;
     
     //__CRC block instantiation__//
 
aurora_64b66b_0_CRC_TOP #(
         .CRC_INIT(CRC_INIT)
         ) tx_crc_gen_i1 (
             .CRCRESET(CRC_RST),
             .CRCCLK(CLK),
             .CRCDATAWIDTH(CRC_DATAWIDTH1),
             .CRCDATAVALID(CRC_DATAVALID1),
             .CRCIN(CRC_DATA1),
             .CRCOUT(CRC1)
     );
 
   assign CRC_DATAWIDTH1 = (!EOF_N_US & ll_valid & (REM_US >= 4'd8) & (REM_US <= 4'd15)) ? REM_US[2:0]   : 3'b111;
   assign CRC_DATAVALID1 = (ll_valid & EOF_N_US)? 1'b1:(ll_valid & !EOF_N_US & (REM_US >= 4'd8 ))?1'b1:1'b0;
   assign CRC_DATA1 = DATA_US[63:0];
  
aurora_64b66b_0_CRC_TOP #(
         .CRC_INIT(CRC_INIT)
         ) tx_crc_gen_i2 (
             .CRCRESET(CRC_RST),
             .CRCCLK(CLK),
             .CRCDATAWIDTH(CRC_DATAWIDTH2),
             .CRCDATAVALID(CRC_DATAVALID2),
             .CRCIN(CRC_DATA2),
             .CRCOUT(CRC2)
     );
 
   assign CRC_DATAWIDTH2 = (!EOF_N_US & ll_valid & (REM_US >= 4'd0) & (REM_US <= 4'd7)) ? REM_US[2:0]   : 3'b111;
   assign CRC_DATAVALID2 = ll_valid;
   assign CRC_DATA2 = DATA_US[127:64];
  
 
 
     //--CRC reset assertion, this initializes the internal crc-reg with CRC_INIT value--//
   assign CRC_RST  = !SOF_N_US && ll_valid;
     //--crc datawidth is equal to REM during EOF--//
   //--data can be input to block during CRC reset--//
 
 assign final_CRC = {
 		CRC2 ,
 		CRC1 }; 
               
 			                                                                                                                              
 	  
     //__Register CRC calculated__//
   always @(posedge CLK)
     CRC_reg <= `DLY final_CRC;
 
     //__Build DS output controls__/
   always @(posedge CLK)
     if (reset_crc_block)
     begin
       SOF_N_DS  <= `DLY 1'b1;
       EOF_N_DS  <= `DLY 1'b1;
       REM_DS    <= `DLY 4'd0;
       SRC_RDY_N_DS<= `DLY 1'b1;
     end
     else if (!DST_RDY_N_DS) 
     begin
       SOF_N_DS  <= `DLY !(sof_ds_r | sof_eof_r | sof_sc_r);
       EOF_N_DS  <= `DLY !(eof_ds_r | sof_eof_r | eof_sc_r);
       REM_DS    <= `DLY (eof_ds_r | sof_eof_r | eof_sc_r) ? rem_out : 4'd0;
       SRC_RDY_N_DS<= `DLY idle_r | src_not_rdy_r | wait_r | sc_frame_r;
     end
     //__Build DS output data__//
   always @(posedge CLK)
     if (reset_crc_block)
       DATA_DS <= `DLY 128'd0;
     else if (!DST_RDY_N_DS) 
     begin     //--depending on input rem, extract corresponding bytes from input data &
             // then append CRC in the next bytes--//
       if (sof_eof_r || eof_ds_r || eof_sc_r)
        DATA_DS <= `DLY 
 (rem_in == 4'd0) ?  {DATA_US_2r[127:120],final_CRC,56'd0} :
 (rem_in == 4'd1) ?  {DATA_US_2r[127:112],final_CRC,48'd0} :
 (rem_in == 4'd2) ?  {DATA_US_2r[127:104],final_CRC,40'd0} :
 (rem_in == 4'd3) ?  {DATA_US_2r[127:96],final_CRC,32'd0} :
 (rem_in == 4'd4) ?  {DATA_US_2r[127:88],final_CRC,24'd0} :
 (rem_in == 4'd5) ?  {DATA_US_2r[127:80],final_CRC,16'd0} :
 (rem_in == 4'd6) ?  {DATA_US_2r[127:72],final_CRC,8'd0} :
 (rem_in == 4'd7)?  {DATA_US_2r[(DATA_WIDTH-1):64],final_CRC} :
 (rem_in == 4'd8)? {CRC_reg[7:0],120'd0}:
 (rem_in == 4'd9)? {CRC_reg[15:0],112'd0}:
 (rem_in == 4'd10)? {CRC_reg[23:0],104'd0}:
 (rem_in == 4'd11)? {CRC_reg[31:0],96'd0}:
 (rem_in == 4'd12)? {CRC_reg[39:0],88'd0}:
 (rem_in == 4'd13)? {CRC_reg[47:0],80'd0}:
 (rem_in == 4'd14)? {CRC_reg[55:0],72'd0}:
                 {CRC_reg,64'd0};
       else if (sof_sc_r || crc_r)
         DATA_DS <= `DLY
               (rem_in == 4'd8)? {DATA_US_2r[(DATA_WIDTH-1):56],final_CRC[(DATA_WIDTH/2-1):8]}:
               (rem_in == 4'd9)? {DATA_US_2r[(DATA_WIDTH-1):48],final_CRC[(DATA_WIDTH/2-1):16]}:
               (rem_in == 4'd10)? {DATA_US_2r[(DATA_WIDTH-1):40],final_CRC[(DATA_WIDTH/2-1):24]}:
               (rem_in == 4'd11)? {DATA_US_2r[(DATA_WIDTH-1):32],final_CRC[(DATA_WIDTH/2-1):32]}:
               (rem_in == 4'd12)? {DATA_US_2r[(DATA_WIDTH-1):24],final_CRC[(DATA_WIDTH/2-1):40]}:
               (rem_in == 4'd13)? {DATA_US_2r[(DATA_WIDTH-1):16],final_CRC[(DATA_WIDTH/2-1):48]}:
               (rem_in == 4'd14)? {DATA_US_2r[(DATA_WIDTH-1):8],final_CRC[(DATA_WIDTH/2-1):56]}:
               DATA_US_2r;
       else  //--otherwise just send the two stage pipeined data to output--//
         DATA_DS <= `DLY DATA_US_2r;
     end
     
 endmodule

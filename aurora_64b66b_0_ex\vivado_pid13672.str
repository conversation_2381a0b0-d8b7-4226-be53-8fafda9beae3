/*

Xilinx Vivado v2021.1 (64-bit) [Major: 2021, Minor: 1]
SW Build: 3247384 on Thu Jun 10 19:36:33 MDT 2021
IP Build: 3246043 on Fri Jun 11 00:30:35 MDT 2021

Process ID (PID): 13672
License: Customer
Mode: GUI Mode

Current time: 	Sat Jul 26 20:55:34 CST 2025
Time zone: 	China Standard Time (Asia/Shanghai)

OS: Windows 10
OS Version: 10.0
OS Architecture: amd64
Available processors (cores): 24

Screen size: 2560x1600
Screen resolution (DPI): 150
Available screens: 2
Default font: family=Dialog,name=Dialog,style=plain,size=18
Scale size: 27

Java version: 	11.0.2 64-bit
Java home: 	G:/Devtools/xilinx_2021.1/Vivado/2021.1/tps/win64/jre11.0.2
Java executable: 	G:/Devtools/xilinx_2021.1/Vivado/2021.1/tps/win64/jre11.0.2/bin/java.exe
Java initial memory (-Xms): 	256 MB
Java maximum memory (-Xmx):	 3 GB


User name: 	Egdon
User home directory: C:/Users/<USER>
User working directory: f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex
User country: 	CN
User language: 	zh
User locale: 	zh_CN

RDI_BASEROOT: G:/Devtools/xilinx_2021.1/Vivado
HDI_APPROOT: G:/Devtools/xilinx_2021.1/Vivado/2021.1
RDI_DATADIR: G:/Devtools/xilinx_2021.1/Vivado/2021.1/data
RDI_BINDIR: G:/Devtools/xilinx_2021.1/Vivado/2021.1/bin

Vivado preferences file: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2021.1/vivado.xml
Vivado preferences directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2021.1/
Vivado layouts directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2021.1/data/layouts
PlanAhead jar file: 	G:/Devtools/xilinx_2021.1/Vivado/2021.1/lib/classes/planAhead.jar
Vivado log file: 	f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/vivado.log
Vivado journal file: 	f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/vivado.jou
Engine tmp dir: 	f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/.Xil/Vivado-13672-DESKTOP-IKVU8M0

Xilinx Environment Variables
----------------------------
XILINX: G:/Devtools/xilinx_2021.1/Vivado/2021.1/ids_lite/ISE
XILINX_DSP: G:/Devtools/xilinx_2021.1/Vivado/2021.1/ids_lite/ISE
XILINX_HLS: G:/Devtools/xilinx_2021.1/Vitis_HLS/2021.1
XILINX_PLANAHEAD: G:/Devtools/xilinx_2021.1/Vivado/2021.1
XILINX_SDK: G:/Devtools/xilinx_2021.1/Vitis/2021.1
XILINX_VITIS: G:/Devtools/xilinx_2021.1/Vitis/2021.1
XILINX_VIVADO: G:/Devtools/xilinx_2021.1/Vivado/2021.1
XILINX_VIVADO_HLS: G:/Devtools/xilinx_2021.1/Vivado/2021.1


GUI allocated memory:	343 MB
GUI max memory:		3,072 MB
Engine allocated memory: 1,368 MB

Copyright 1986-2021 Xilinx, Inc. All Rights Reserved.

*/

// TclEventType: START_GUI
// TclEventType: START_PROGRESS_DIALOG
// Tcl Message: start_gui 
// TclEventType: START_PROGRESS_DIALOG
// bz (cs):  Sourcing Tcl script 'f:/project/FPGA/test/mk7100_aurora_test/mk7100_aurora_test.gen/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0_ex.tcl' : addNotify
// TclEventType: DEBUG_PROBE_SET_CHANGE
// TclEventType: FLOW_ADDED
// Tcl Message: source f:/project/FPGA/test/mk7100_aurora_test/mk7100_aurora_test.gen/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0_ex.tcl -notrace 
// Tcl Message: INFO: [open_example_project] Creating new example project... 
// TclEventType: FILE_SET_NEW
// TclEventType: RUN_ADD
// TclEventType: RUN_CURRENT
// TclEventType: PROJECT_DASHBOARD_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_NEW
// [GUI Memory]: 103 MB (+105976kb) [00:00:03]
// [Engine Memory]: 1,368 MB (+1282828kb) [00:00:03]
// [GUI Memory]: 128 MB (+20765kb) [00:00:04]
// [GUI Memory]: 137 MB (+1896kb) [00:00:04]
// WARNING: HEventQueue.dispatchEvent() is taking  1290 ms.
// TclEventType: FILE_SET_OPTIONS_CHANGE
// Tcl Message: INFO: [IP_Flow 19-234] Refreshing IP repositories INFO: [IP_Flow 19-1704] No user IP repositories specified INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'G:/Devtools/xilinx_2021.1/Vivado/2021.1/data/ip'. 
// TclEventType: FILE_SET_OPTIONS_CHANGE
// TclEventType: RUN_MODIFY
// TclEventType: CREATE_IP_CATALOG
// TclEventType: PART_MODIFIED
// TclEventType: FILE_SET_CHANGE
// Tcl Message: INFO: [open_example_project] Importing original IP ... 
// TclEventType: FILE_SET_CHANGE
// Tcl Message: INFO: [open_example_project] Generating the example project IP ... 
// Tcl Message: INFO: [IP_Flow 19-1706] Not generating 'Instantiation Template' target for IP 'aurora_64b66b_0'. Target already exists and is up to date. INFO: [IP_Flow 19-1706] Not generating 'Synthesis' target for IP 'aurora_64b66b_0'. Target already exists and is up to date. INFO: [IP_Flow 19-1706] Not generating 'Simulation' target for IP 'aurora_64b66b_0'. Target already exists and is up to date. INFO: [IP_Flow 19-1706] Not generating 'Shared Logic Files' target for IP 'aurora_64b66b_0'. Target already exists and is up to date. 
// Tcl Message: INFO: [open_example_project] Adding example synthesis HDL files ... INFO: [open_example_project] Adding example XDC files ... INFO: [open_example_project] Adding simulation HDL files ... 
// HMemoryUtils.trashcanNow. Engine heap size: 1,368 MB. GUI used memory: 76 MB. Current time: 7/26/25, 8:55:35 PM CST
// TclEventType: FILE_SET_CHANGE
// TclEventType: FILE_SET_OPTIONS_CHANGE
// TclEventType: CREATE_IP_CORE
// TclEventType: LOAD_FEATURE
// TclEventType: REPORT_IP_STATUS_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: FILESET_UPDATE_IP
// TclEventType: PROJ_DESIGN_MODE_SET
// TclEventType: FILE_SET_CHANGE
// TclEventType: CREATE_IP_CORE
// TclEventType: REPORT_IP_STATUS_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: FILESET_UPDATE_IP
// TclEventType: PROJ_DESIGN_MODE_SET
// TclEventType: FILE_SET_CHANGE
// Tcl Message: INFO: [open_example_project] Sourcing example extension scripts ... INFO: [open_example_project] Sourcing extension script: exdes_pre_bdgen.tcl INFO: [open_example_project] Sourcing extension script: exdes_pre_bdgen_partner.tcl INFO: [open_example_project] Sourcing extension script: aurora_64b66b_0/tcl/gen_axi4strm_reg_slice.tcl 
// Tcl Message: INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'aurora_64b66b_0_reg_slice_0'... INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'aurora_64b66b_0_reg_slice_0'... INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'aurora_64b66b_0_reg_slice_0'... INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'aurora_64b66b_0_reg_slice_0'... INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'aurora_64b66b_0_reg_slice_0'... 
// Tcl Message: INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'aurora_64b66b_0_reg_slice_2'... INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'aurora_64b66b_0_reg_slice_2'... INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'aurora_64b66b_0_reg_slice_2'... INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'aurora_64b66b_0_reg_slice_2'... INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'aurora_64b66b_0_reg_slice_2'... 
// TclEventType: FILE_SET_CHANGE
// TclEventType: DG_GRAPH_STALE
// TclEventType: PROJECT_CHANGE
// Tcl Message: INFO: [open_example_project] Rebuilding all the top level IPs ... 
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PROJECT_CHANGE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PROJECT_CHANGE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: PACKAGER_MESSAGE_RESET
// TclEventType: PACKAGER_UNLOAD_CORE
// TclEventType: STOP_PROGRESS_DIALOG
// Tcl Message: INFO: [open_example_project] Open Example Project completed 
dismissDialog("Sourcing Tcl script 'f:/project/FPGA/test/mk7100_aurora_test/mk7100_aurora_test.gen/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0_ex.tcl'"); // bz
// TclEventType: STOP_PROGRESS_DIALOG
// WARNING: HEventQueue.dispatchEvent() is taking  5315 ms.
// [GUI Memory]: 183 MB (+41131kb) [00:00:19]
// Tcl Message: update_compile_order -fileset sources_1 
// Tcl Command: 'rdi::info_commands {device::*}'
// Tcl Command: 'rdi::info_commands {debug::*}'
// Tcl Command: 'rdi::info_commands {*}'
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1, true); // D - Node
// [GUI Memory]: 194 MB (+2681kb) [00:00:23]
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1, true, false, false, false, false, true); // D - Double Click - Node
// WARNING: HEventQueue.dispatchEvent() is taking  1235 ms.
// Elapsed time: 17 seconds
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_chk_axi_to_ll_data_i : aurora_64b66b_0_EXAMPLE_AXI_TO_LL (aurora_64b66b_0_example_axi_to_ll.v)]", 6, true); // D - Node
// Elapsed time: 36 seconds
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), aurora_64b66b_0_block_i : aurora_64b66b_0 (aurora_64b66b_0.xci)]", 2, false); // D
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), aurora_64b66b_0_block_i : aurora_64b66b_0 (aurora_64b66b_0.xci)]", 2, false, false, false, false, false, true); // D - Double Click
// Run Command: PAResourceCommand.PACommandNames_RECUSTOMIZE_CORE
// [GUI Memory]: 206 MB (+1730kb) [00:01:22]
// N (cs):  Re-customize IP : addNotify
// r (cs): Re-customize IP: addNotify
dismissDialog("Re-customize IP"); // N
selectTab(PAResourceEtoH.HACGCTabbedPane_TABBED_PANE, (HResource) null, "GT Selections", 1); // bi
selectTab(PAResourceEtoH.HACGCTabbedPane_TABBED_PANE, (HResource) null, "Shared Logic", 2); // bi
selectTab(PAResourceEtoH.HACGCTabbedPane_TABBED_PANE, (HResource) null, "GT Selections", 1); // bi
// HMemoryUtils.trashcanNow. Engine heap size: 1,429 MB. GUI used memory: 143 MB. Current time: 7/26/25, 8:56:55 PM CST
selectTab(PAResourceEtoH.HACGCTabbedPane_TABBED_PANE, (HResource) null, "Shared Logic", 2); // bi
dismissDialog("Re-customize IP"); // r
// Elapsed time: 11 seconds
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v)]", 1); // D
// Run Command: PAResourceCommand.PACommandNames_RECUSTOMIZE_CORE
// r (cs): Re-customize IP: addNotify
dismissDialog("Re-customize IP"); // r
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4); // D
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4); // D
// Elapsed time: 140 seconds
selectCodeEditor("aurora_64b66b_0_exdes.v", 262, 794); // bP
selectCodeEditor("aurora_64b66b_0_exdes.v", 262, 794, false, false, false, false, true); // bP - Double Click
// Elapsed time: 24 seconds
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), genblk3.u_rst_sync_gtrsttmpi : aurora_64b66b_0_rst_sync_exdes (aurora_64b66b_0_cdc_sync_exdes.v)]", 3, false); // D
// Elapsed time: 31 seconds
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4, true); // D - Node
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4, true, false, false, false, true, false); // D - Popup Trigger - Node
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4, true); // D - Node
// [Engine Memory]: 1,440 MB (+4202kb) [00:05:12]
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4); // D
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_gen_ll_to_axi_data_i : aurora_64b66b_0_EXAMPLE_LL_TO_AXI (aurora_64b66b_0_example_ll_to_axi.v)]", 4); // D
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_chk_axi_to_ll_data_i : aurora_64b66b_0_EXAMPLE_AXI_TO_LL (aurora_64b66b_0_example_axi_to_ll.v)]", 6); // D
collapseTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, aurora_64b66b_0_exdes (aurora_64b66b_0_exdes.v), core_traffic.frame_chk_axi_to_ll_data_i : aurora_64b66b_0_EXAMPLE_AXI_TO_LL (aurora_64b66b_0_example_axi_to_ll.v)]", 6); // D

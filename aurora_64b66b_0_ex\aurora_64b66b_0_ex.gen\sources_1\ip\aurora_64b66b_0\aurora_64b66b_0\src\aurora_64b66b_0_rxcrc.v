 ///////////////////////////////////////////////////////////////////////////////
 // Project:  Aurora 64B/66B
 // Company:  Xilinx
 //
 //
 // (c) Copyright 2008 - 2009 Xilinx, Inc. All rights reserved.
 //
 // This file contains confidential and proprietary information
 // of Xilinx, Inc. and is protected under U.S. and
 // international copyright and other intellectual property
 // laws.
 //
 // DISCLAIMER
 // This disclaimer is not a license and does not grant any
 // rights to the materials distributed herewith. Except as
 // otherwise provided in a valid license issued to you by
 // Xilinx, and to the maximum extent permitted by applicable
 // law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
 // WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
 // AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
 // BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
 // INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
 // (2) Xilinx shall not be liable (whether in contract or tort,
 // including negligence, or under any other theory of
 // liability) for any loss or damage of any kind or nature
 // related to, arising under or in connection with these
 // materials, including for any direct, or any indirect,
 // special, incidental, or consequential loss or damage
 // (including loss of data, profits, goodwill, or any type of
 // loss or damage suffered as a result of any action brought
 // by a third party) even if such damage or loss was
 // reasonably foreseeable or Xilinx had been advised of the
 // possibility of the same.
 //
 // CRITICAL APPLICATIONS
 // Xilinx products are not designed or intended to be fail-
 // safe, or for use in any application requiring fail-safe
 // performance, such as life-support or safety devices or
 // systems, Class III medical devices, nuclear facilities,
 // applications related to the deployment of airbags, or any
 // other applications that could lead to death, personal
 // injury, or severe property or environmental damage
 // (individually and collectively, "Critical
 // Applications"). Customer assumes the sole risk and
 // liability of any use of Xilinx products in Critical
 // Applications, subject only to applicable laws and
 // regulations governing limitations on product liability.
 //
 // THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
 // PART OF THIS FILE AT ALL TIMES.
 
 /////////////////////////////////////////////////////////////////////////////////////
 //
 // Module DESCRAMBLER_64B66B
 // Generated by Xilinx Aurora 64B66B
 
 `timescale 1ns / 10ps
 `define DLY #1
(* DowngradeIPIdentifiedWarnings="yes" *) 
 //***********************************Entity Declaration*******************************
 module aurora_64b66b_0_RX_CRC (
   DATA_DS,
   REM_DS,
   SOF_N_DS,
   EOF_N_DS,
   SRC_RDY_N_DS,
   DST_RDY_N_DS,
   DST_RDY_N_US,
   CRC_PASS_FAIL_N,
   CRC_VALID,
   DATA_US,
   REM_US,
   SOF_N_US,
   EOF_N_US,
   SRC_RDY_N_US,
   RESET,
   CHANNEL_UP,
   CLK
 );
 
 parameter CRC_INIT = 32'hFFFFFFFF;
 parameter DATA_WIDTH = 128;
 parameter REM_WIDTH = 4;
 
 
   output [(DATA_WIDTH-1):0] DATA_DS;
   output [(REM_WIDTH-1):0]  REM_DS;                   
   output      	SOF_N_DS;
   output      	EOF_N_DS;
   output      	SRC_RDY_N_DS;
   input       	DST_RDY_N_DS;
   output      	DST_RDY_N_US;
   output      	CRC_PASS_FAIL_N;
   output      	CRC_VALID;
   input [(DATA_WIDTH-1):0]  DATA_US;
   input [(REM_WIDTH-1):0]   REM_US;                   
   input       	SOF_N_US;
   input       	EOF_N_US;
   input       	SRC_RDY_N_US;
   input       	RESET;
   input       	CHANNEL_UP;
   input       	CLK;
     
   reg [(DATA_WIDTH-1):0]    DATA_DS;
   reg [(REM_WIDTH-1):0]     REM_DS;                   
   reg       	SOF_N_DS;
   reg       	EOF_N_DS;
   reg       	SRC_RDY_N_DS;
     
   reg       	CRC_VALID;
     
     //__Signal declaration for one-hot encoded FSM__//
     reg   idle_r;   //--Idle or power on state
     reg   sc_frame_r; //--single cycle frame received state
     reg   sof_r;    //--sof received state (not single cycle frame)
     reg   sof_eof_r;  //--build single cycle frame output--//
     reg   sof_ds_r; //--build sof state
     reg   data_r;   //--data state
     reg   eof_ds_r; //--build eof state
     reg   crc_r;    //--final CRC state
     
     wire  idle_c;
     wire  sc_frame_c;
     wire  sof_c;
     wire  sof_eof_c;
     wire  sof_ds_c;
     wire  data_c;
     wire  eof_ds_c;
     wire  crc_c;
     
     //__internal register declaration__//
     reg     	EOF_N_US_r;   //--registered EOF input
     reg     	SOF_N_US_r;   //--registered SOF input
     reg [(4-1):0] 	rem_in;     //--REM input
     wire [(4-1):0] 	rem_int_i;     //--REM input
     reg [(4-1):0]   rem_out;    //--REM output
     reg [(DATA_WIDTH-1):0]  DATA_US_r;    //--data pipe-I
     reg [(DATA_WIDTH-1):0]  DATA_US_2r;   //--data pipe-II
     reg     	SRC_RDY_N_US_2r;
     reg     	ll_valid_r;   //--LL valid registered
     reg [1:0] 	count;      //--count to deassert dst-rdy-us after eof reception
     reg [(DATA_WIDTH/2-1):0]  received_CRC; //--received CRC extracted and stored in this register
     reg 			CRC_RESET_r=1'b0;
     reg                       EOF_N_US_r1;   //-- continously registered EOF input
     reg                       EOF_N_US_r2;   //-- continously registered EOF input
     reg                       load_waiting_crc;   //-- if consecutive or close by (1 cycle apart) EOF appeared, received_crc had to be hold for one cycle, this signal loads the current value afterwards

     
     //__internal wire declaration__//
     wire    	CRC_RESET;
   wire    		CRC_DATAVALID;   //__LocalLink valid
   wire    		CRC_DATAVALID1;   //__LocalLink valid
   wire [31:0] 	        CRC1;      //__CRC calculated
   wire [2:0]  	        CRC_DATAWIDTH1;  //__CRC datawidth
   wire [63:0] 	        CRC_DATA1;   //__data input to CRC block
   wire    		CRC_DATAVALID2;   //__LocalLink valid
   wire [31:0] 	        CRC2;      //__CRC calculated
   wire [2:0]  	        CRC_DATAWIDTH2;  //__CRC datawidth
   wire [63:0] 	        CRC_DATA2;   //__data input to CRC block
     wire    	ll_valid;
     wire    	DST_RDY_N_US_i;
     wire [(DATA_WIDTH/2-1):0] tx_crc_sc;
     wire [(DATA_WIDTH/2-1):0] tx_crc;
     wire [(DATA_WIDTH/2-1):0] final_CRC;
    
  
     //____________Main code begins here________________//

  assign reset_crc_block = RESET || !CHANNEL_UP;
     
     //__Initialization & state assignment for FSM__//
   always @(posedge CLK)
     if (reset_crc_block)
     begin
       idle_r    <= `DLY 1'b1;
       sc_frame_r  <= `DLY 1'b0;
       sof_r   <= `DLY 1'b0;
       sof_eof_r <= `DLY 1'b0;
       sof_ds_r  <= `DLY 1'b0;
       data_r    <= `DLY 1'b0;
       eof_ds_r  <= `DLY 1'b0;
       crc_r   <= `DLY 1'b0;
     end
     else if (!DST_RDY_N_DS)
     begin
       idle_r    <= `DLY idle_c;
       sc_frame_r  <= `DLY sc_frame_c;
       sof_r   <= `DLY sof_c;
       sof_eof_r <= `DLY sof_eof_c;
       sof_ds_r  <= `DLY sof_ds_c;
       data_r    <= `DLY data_c;
       eof_ds_r  <= `DLY eof_ds_c;
       crc_r   <= `DLY crc_c;
     end
     
     //__Combinatorial logic for FSM__//
     
     //--stay in idle when there is no input SOF or 
     //when current frame calculations complete--//
   assign idle_c = ((idle_r || eof_ds_r || sof_eof_r) & 
                 (SOF_N_US || !ll_valid));
     //--reach this when single cycle frame is received--//  
   assign sc_frame_c = (idle_r || eof_ds_r || sof_eof_r) & 
                   !SOF_N_US & !EOF_N_US & ll_valid;
     //--reach this when non-single cycle frame is received--//
   assign sof_c  = ((idle_r || eof_ds_r || sof_eof_r) & 
                 !SOF_N_US & EOF_N_US & ll_valid) ||
 						(sof_r && !ll_valid);
     //--build single cycle frame output--//
   assign sof_eof_c  = (sof_r & ll_valid & !EOF_N_US & (REM_US < 4'd8)) || sc_frame_r;
     //--build SOF output--//
   assign sof_ds_c   = sof_r & ll_valid & ((!EOF_N_US & (REM_US > 4'd7)) | EOF_N_US);
     //--data input state--//
   assign data_c = (sof_ds_r & ((EOF_N_US & EOF_N_US_r) || !ll_valid)) ||
               (data_r & (EOF_N_US || !ll_valid));
     //--final CRC state--//
   assign crc_c  = (sof_ds_r & !EOF_N_US & (REM_US > 4'd7) & ll_valid) ||
             (data_r & !EOF_N_US & (REM_US > 4'd7) & ll_valid);
     //--build EOF output--//        
   assign eof_ds_c =(sof_ds_r & ((!EOF_N_US_r & ll_valid_r) || (!EOF_N_US & (REM_US < 4'd8) & ll_valid))) ||
             (data_r & !EOF_N_US & (REM_US < 4'd8) & ll_valid) || crc_r; 
   
     //__Store REM value and build output REM__//
   always @(posedge CLK)
     if (!EOF_N_US & ll_valid)
     begin
       rem_out <= `DLY (REM_US > 4'd6)? REM_US + 4'd9: REM_US + 4'd9;
       rem_in  <= `DLY REM_US;
     end
       
     //__input signals registered__//
   always @(posedge CLK)
     if (ll_valid)
     begin
       EOF_N_US_r    <= `DLY EOF_N_US;
       SOF_N_US_r    <= `DLY SOF_N_US;
       DATA_US_r   <= `DLY DATA_US;
     end
     //--register data and src rdy inputs--//
   always @(posedge CLK)
   if (!DST_RDY_N_DS || !EOF_N_US_r)
   begin
     DATA_US_2r    <= `DLY DATA_US_r;
     SRC_RDY_N_US_2r <= `DLY !CRC_DATAVALID;
   end
 
     //__input signals registered__
   always @(posedge CLK) begin
      EOF_N_US_r1  <= `DLY EOF_N_US;   
      EOF_N_US_r2  <= `DLY EOF_N_US_r1; 
   end // always @ (posedge CLK)

   
   //__store the received CRC__
   always @(posedge CLK) begin
      if (!SOF_N_US && !EOF_N_US & ll_valid) 
       begin
         if(EOF_N_US_r1) begin
            received_CRC  <= `DLY tx_crc_sc;
            load_waiting_crc <= 1'b0;
         end
         else 
            load_waiting_crc <= 1'b1;
       end
      else if (load_waiting_crc) 
       begin
         received_CRC  <= `DLY tx_crc_sc;
         load_waiting_crc <= 1'b0;
       end
      else if (!EOF_N_US && SOF_N_US & ll_valid)
        received_CRC  <= `DLY tx_crc;
      end // always @ (posedge CLK)
 
aurora_64b66b_0_CRC_TOP #(
         .CRC_INIT(CRC_INIT)
         ) rx_crc_gen_i1 (
             .CRCRESET(CRC_RESET),
             .CRCCLK(CLK),
             .CRCDATAWIDTH(CRC_DATAWIDTH1),
             .CRCDATAVALID(CRC_DATAVALID1),
             .CRCIN(CRC_DATA1),
             .CRCOUT(CRC1)
     );
 
   assign CRC_DATAWIDTH1 = ((rem_int_i >= 4'd8) & (rem_int_i <= 4'd15)) ? rem_int_i[2:0]   : 3'b111;
   assign CRC_DATAVALID1 = CRC_DATAVALID && (rem_int_i > 4'd7) ;
   assign CRC_DATA1 = DATA_US_r[63:0];
  
aurora_64b66b_0_CRC_TOP #(
         .CRC_INIT(CRC_INIT)
         ) rx_crc_gen_i2 (
             .CRCRESET(CRC_RESET),
             .CRCCLK(CLK),
             .CRCDATAWIDTH(CRC_DATAWIDTH2),
             .CRCDATAVALID(CRC_DATAVALID2),
             .CRCIN(CRC_DATA2),
             .CRCOUT(CRC2)
     );
 
   assign CRC_DATAWIDTH2 = ((rem_int_i >= 4'd0) & (rem_int_i <= 4'd7)) ? rem_int_i[2:0]   : 3'b111;
   assign CRC_DATAVALID2 = CRC_DATAVALID;
   assign CRC_DATA2 = DATA_US_r[127:64];
  
     
     //--CRC reset is give one cycle later so that REM information
     //which translates into crc-data-width can be calculated early--//
 	assign CRC_RESET  = (!SOF_N_US_r & ll_valid_r) ? (ll_valid || !EOF_N_US_r) : (ll_valid && CRC_RESET_r);
 
 	always @(posedge CLK)
       if (!SOF_N_US_r & EOF_N_US_r & ll_valid_r)
          CRC_RESET_r <= `DLY !ll_valid;
       else if (ll_valid & CRC_RESET_r)
          CRC_RESET_r <= `DLY 1'b0;
 
 	//--crc datawidth depending on input REM--//
   assign rem_int_i = (!EOF_N_US & (REM_US < 4'd8)) ? REM_US + 4'd8 :
                (!EOF_N_US_r & (rem_in > 4'd7 )) ? rem_in + 4'd8 : 4'd15;
 
       //when input REM > 3 during input EOF--//
 	assign CRC_DATAVALID = (!SOF_N_US && ll_valid) ? 1'b0 :
             ((!EOF_N_US_r & (rem_in > 4'd7) & ll_valid_r) || ll_valid || CRC_RESET);
 
  
       //--Build dst-rdy-us output--//
   assign DST_RDY_N_US = DST_RDY_N_US_i;
   assign DST_RDY_N_US_i = DST_RDY_N_DS || |count;
   
   //__deassert dst_rdy_n_us for 2 cycles after eof_n_us reception__//
       
   always @(posedge CLK)
     if (reset_crc_block)
       count <= `DLY 2'b00;
     else if (!EOF_N_US & ll_valid)
       //count <=  2'b10;
       count <= `DLY 2'b01;
     else if (|count && !DST_RDY_N_DS)
       count <= `DLY count >> 1;
     //--Local Link valid--//  
   assign ll_valid = !SRC_RDY_N_US ;
     //--register ll-valid for use--//
   always @(posedge CLK)
     ll_valid_r  <= `DLY ll_valid;
     
     //__Build DS output__//
   always @(posedge CLK)
     if (reset_crc_block)
     begin
       SOF_N_DS  <= `DLY 1'b1;
       EOF_N_DS  <= `DLY 1'b1;
       SRC_RDY_N_DS<= `DLY 1'b1;
       REM_DS    <= `DLY 4'd0;
       DATA_DS   <= `DLY 128'd0;
     end
     else if (!DST_RDY_N_DS)
     begin
       SOF_N_DS  <= `DLY !(sof_eof_r || sof_ds_r);
       EOF_N_DS  <= `DLY !(sof_eof_r || eof_ds_r);
       SRC_RDY_N_DS<= `DLY SRC_RDY_N_US_2r;
       REM_DS    <= `DLY (sof_eof_r || eof_ds_r) ? rem_out : 4'd0;
       DATA_DS   <= `DLY DATA_US_2r;
     end
     
     //__Extract CRC from input data__//
     
     //__CRC extraction for single cycle frame inputs__//
   assign tx_crc_sc  = 
             (REM_US == 4'd8) ? DATA_US[119:56]: 
             (REM_US == 4'd9) ? DATA_US[111:48]: 
             (REM_US == 4'd10) ? DATA_US[103:40]: 
             (REM_US == 4'd11) ? DATA_US[95:32]: 
             (REM_US == 4'd12) ? DATA_US[87:24]: 
             (REM_US == 4'd13) ? DATA_US[79:16]: 
             (REM_US == 4'd14) ? DATA_US[71:8]: 
              DATA_US[63:0]; 
     //__CRC extraction for all other cases__//      
   assign tx_crc =
             (REM_US == 4'd0) ? {DATA_US_r[55:0],DATA_US[127:120]} :
             (REM_US == 4'd1) ? {DATA_US_r[47:0],DATA_US[127:112]} :
             (REM_US == 4'd2) ? {DATA_US_r[39:0],DATA_US[127:104]} :
             (REM_US == 4'd3) ? {DATA_US_r[31:0],DATA_US[127:96]} :
             (REM_US == 4'd4) ? {DATA_US_r[23:0],DATA_US[127:88]} :
             (REM_US == 4'd5) ? {DATA_US_r[15:0],DATA_US[127:80]} :
             (REM_US == 4'd6) ? {DATA_US_r[7:0],DATA_US[127:72]} :
             (REM_US == 4'd7) ? DATA_US[127:64]:
             (REM_US == 4'd8) ? DATA_US[119:56]:
             (REM_US == 4'd9) ? DATA_US[111:48]:
             (REM_US == 4'd10) ? DATA_US[103:40]:
             (REM_US == 4'd11) ? DATA_US[95:32]:
             (REM_US == 4'd12) ? DATA_US[87:24]:
             (REM_US == 4'd13) ? DATA_US[79:16]:
             (REM_US == 4'd14) ? DATA_US[71:8]:
              DATA_US[63:0];
       
   assign final_CRC = {
 		CRC2 ,
 		CRC1 }; 
      
          //__compare the received CRC with the CRC calculated__//
   assign CRC_PASS_FAIL_N  = (received_CRC == final_CRC);
       
       //__CRC_PASS_FAIL_N to be considered only when CRC_VALID is asserted__//
   always @(posedge CLK)
     if (reset_crc_block)
       CRC_VALID <= `DLY 1'b0;
     else
       CRC_VALID <= `DLY (sof_eof_r || eof_ds_r) & (!ll_valid);
       
 endmodule
 

<?xml version="1.0" encoding="UTF-8"?>
<!-- Product Version: Vivado v2021.1 (64-bit)              -->
<!--                                                         -->
<!-- Copyright 1986-2021 Xilinx, Inc. All Rights Reserved.   -->

<Project Version="7" Minor="55" Path="f:/project/FPGA/test/mk7100_aurora_example/aurora_64b66b_0_ex/aurora_64b66b_0_ex.xpr">
  <DefaultLaunch Dir="$PRUNDIR"/>
  <Configuration>
    <Option Name="Id" Val="50fb939a74d94eacad8d4260b97d7436"/>
    <Option Name="Part" Val="xc7z100ffg900-2"/>
    <Option Name="CompiledLibDir" Val="$PCACHEDIR/compile_simlib"/>
    <Option Name="CompiledLibDirXSim" Val=""/>
    <Option Name="CompiledLibDirModelSim" Val="$PCACHEDIR/compile_simlib/modelsim"/>
    <Option Name="CompiledLibDirQuesta" Val="$PCACHEDIR/compile_simlib/questa"/>
    <Option Name="CompiledLibDirIES" Val="$PCACHEDIR/compile_simlib/ies"/>
    <Option Name="CompiledLibDirXcelium" Val="$PCACHEDIR/compile_simlib/xcelium"/>
    <Option Name="CompiledLibDirVCS" Val="$PCACHEDIR/compile_simlib/vcs"/>
    <Option Name="CompiledLibDirRiviera" Val="$PCACHEDIR/compile_simlib/riviera"/>
    <Option Name="CompiledLibDirActivehdl" Val="$PCACHEDIR/compile_simlib/activehdl"/>
    <Option Name="SimulatorInstallDirModelSim" Val=""/>
    <Option Name="SimulatorInstallDirQuesta" Val=""/>
    <Option Name="SimulatorInstallDirIES" Val=""/>
    <Option Name="SimulatorInstallDirXcelium" Val=""/>
    <Option Name="SimulatorInstallDirVCS" Val=""/>
    <Option Name="SimulatorInstallDirRiviera" Val=""/>
    <Option Name="SimulatorInstallDirActiveHdl" Val=""/>
    <Option Name="SimulatorGccInstallDirModelSim" Val=""/>
    <Option Name="SimulatorGccInstallDirQuesta" Val=""/>
    <Option Name="SimulatorGccInstallDirIES" Val=""/>
    <Option Name="SimulatorGccInstallDirXcelium" Val=""/>
    <Option Name="SimulatorGccInstallDirVCS" Val=""/>
    <Option Name="SimulatorGccInstallDirRiviera" Val=""/>
    <Option Name="SimulatorGccInstallDirActiveHdl" Val=""/>
    <Option Name="SimulatorVersionXsim" Val="2021.1"/>
    <Option Name="SimulatorVersionModelSim" Val="2020.4"/>
    <Option Name="SimulatorVersionQuesta" Val="2020.4"/>
    <Option Name="SimulatorVersionIES" Val="15.20.083"/>
    <Option Name="SimulatorVersionXcelium" Val="20.09.006"/>
    <Option Name="SimulatorVersionVCS" Val="R-2020.12"/>
    <Option Name="SimulatorVersionRiviera" Val="2020.10"/>
    <Option Name="SimulatorVersionActiveHdl" Val="12.0"/>
    <Option Name="SimulatorGccVersionXsim" Val="6.2.0"/>
    <Option Name="SimulatorGccVersionModelSim" Val="5.3.0"/>
    <Option Name="SimulatorGccVersionQuesta" Val="5.3.0"/>
    <Option Name="SimulatorGccVersionIES" Val="6.2.0"/>
    <Option Name="SimulatorGccVersionXcelium" Val="6.3"/>
    <Option Name="SimulatorGccVersionVCS" Val="6.2.0"/>
    <Option Name="SimulatorGccVersionRiviera" Val="6.2.0"/>
    <Option Name="SimulatorGccVersionActiveHdl" Val="6.2.0"/>
    <Option Name="BoardPart" Val=""/>
    <Option Name="ActiveSimSet" Val="sim_1"/>
    <Option Name="DefaultLib" Val="xil_defaultlib"/>
    <Option Name="ProjectType" Val="Default"/>
    <Option Name="IPOutputRepo" Val="$PCACHEDIR/ip"/>
    <Option Name="IPDefaultOutputPath" Val="$PGENDIR/sources_1"/>
    <Option Name="IPCachePermission" Val="read"/>
    <Option Name="IPCachePermission" Val="write"/>
    <Option Name="EnableCoreContainer" Val="FALSE"/>
    <Option Name="CreateRefXciForCoreContainers" Val="FALSE"/>
    <Option Name="IPUserFilesDir" Val="$PIPUSERFILESDIR"/>
    <Option Name="IPStaticSourceDir" Val="$PIPUSERFILESDIR/ipstatic"/>
    <Option Name="EnableBDX" Val="FALSE"/>
    <Option Name="WTXSimLaunchSim" Val="0"/>
    <Option Name="WTModelSimLaunchSim" Val="0"/>
    <Option Name="WTQuestaLaunchSim" Val="0"/>
    <Option Name="WTIesLaunchSim" Val="0"/>
    <Option Name="WTVcsLaunchSim" Val="0"/>
    <Option Name="WTRivieraLaunchSim" Val="0"/>
    <Option Name="WTActivehdlLaunchSim" Val="0"/>
    <Option Name="WTXSimExportSim" Val="3"/>
    <Option Name="WTModelSimExportSim" Val="3"/>
    <Option Name="WTQuestaExportSim" Val="3"/>
    <Option Name="WTIesExportSim" Val="3"/>
    <Option Name="WTVcsExportSim" Val="3"/>
    <Option Name="WTRivieraExportSim" Val="3"/>
    <Option Name="WTActivehdlExportSim" Val="3"/>
    <Option Name="GenerateIPUpgradeLog" Val="TRUE"/>
    <Option Name="XSimRadix" Val="hex"/>
    <Option Name="XSimTimeUnit" Val="ns"/>
    <Option Name="XSimArrayDisplayLimit" Val="1024"/>
    <Option Name="XSimTraceLimit" Val="65536"/>
    <Option Name="SimTypes" Val="rtl"/>
    <Option Name="SimTypes" Val="bfm"/>
    <Option Name="SimTypes" Val="tlm"/>
    <Option Name="SimTypes" Val="tlm_dpi"/>
    <Option Name="MEMEnableMemoryMapGeneration" Val="TRUE"/>
    <Option Name="DcpsUptoDate" Val="TRUE"/>
    <Option Name="ClassicSocBoot" Val="FALSE"/>
  </Configuration>
  <FileSets Version="1" Minor="31">
    <FileSet Name="sources_1" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1" RelGenDir="$PGENDIR/sources_1">
      <Filter Type="Srcs"/>
      <File Path="$PSRCDIR/sources_1/ip/aurora_64b66b_0_reg_slice_2/aurora_64b66b_0_reg_slice_2.xci">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/ip/aurora_64b66b_0_reg_slice_0/aurora_64b66b_0_reg_slice_0.xci">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PSRCDIR/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0.xci">
        <FileInfo>
          <Attr Name="ImportPath" Val="$PPRDIR/../../mk7100_aurora_test/mk7100_aurora_test.srcs/sources_1/ip/aurora_64b66b_0/aurora_64b66b_0.xci"/>
          <Attr Name="ImportTime" Val="1753534403"/>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_cdc_sync_exdes.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_example_axi_to_ll.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_example_ll_to_axi.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_frame_check.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_frame_gen.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_exdes.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <Config>
        <Option Name="DesignMode" Val="RTL"/>
        <Option Name="TopModule" Val="aurora_64b66b_0_exdes"/>
      </Config>
    </FileSet>
    <FileSet Name="constrs_1" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1" RelGenDir="$PGENDIR/constrs_1">
      <Filter Type="Constrs"/>
      <File Path="$PPRDIR/imports/aurora_64b66b_0_exdes.xdc">
        <FileInfo>
          <Attr Name="UsedIn" Val="synthesis"/>
          <Attr Name="UsedIn" Val="implementation"/>
        </FileInfo>
      </File>
      <Config>
        <Option Name="ConstrsType" Val="XDC"/>
      </Config>
    </FileSet>
    <FileSet Name="sim_1" Type="SimulationSrcs" RelSrcDir="$PSRCDIR/sim_1" RelGenDir="$PGENDIR/sim_1">
      <File Path="$PPRDIR/imports/aurora_64b66b_0_tb.v">
        <FileInfo>
          <Attr Name="UsedIn" Val="implementation"/>
          <Attr Name="UsedIn" Val="simulation"/>
        </FileInfo>
      </File>
      <Config>
        <Option Name="DesignMode" Val="RTL"/>
        <Option Name="TopModule" Val="aurora_64b66b_0_TB"/>
        <Option Name="TopLib" Val="xil_defaultlib"/>
        <Option Name="TopAutoSet" Val="TRUE"/>
        <Option Name="TransportPathDelay" Val="0"/>
        <Option Name="TransportIntDelay" Val="0"/>
        <Option Name="SelectedSimModel" Val="rtl"/>
        <Option Name="PamDesignTestbench" Val=""/>
        <Option Name="PamDutBypassFile" Val="xil_dut_bypass"/>
        <Option Name="PamSignalDriverFile" Val="xil_bypass_driver"/>
        <Option Name="PamPseudoTop" Val="pseudo_tb"/>
        <Option Name="SrcSet" Val="sources_1"/>
      </Config>
    </FileSet>
    <FileSet Name="utils_1" Type="Utils" RelSrcDir="$PSRCDIR/utils_1" RelGenDir="$PGENDIR/utils_1">
      <Filter Type="Utils"/>
      <Config>
        <Option Name="TopAutoSet" Val="TRUE"/>
      </Config>
    </FileSet>
  </FileSets>
  <Simulators>
    <Simulator Name="XSim">
      <Option Name="Description" Val="Vivado Simulator"/>
      <Option Name="CompiledLib" Val="0"/>
    </Simulator>
    <Simulator Name="ModelSim">
      <Option Name="Description" Val="ModelSim Simulator"/>
    </Simulator>
    <Simulator Name="Questa">
      <Option Name="Description" Val="Questa Advanced Simulator"/>
    </Simulator>
    <Simulator Name="Riviera">
      <Option Name="Description" Val="Riviera-PRO Simulator"/>
    </Simulator>
    <Simulator Name="ActiveHDL">
      <Option Name="Description" Val="Active-HDL Simulator"/>
    </Simulator>
  </Simulators>
  <Runs Version="1" Minor="15">
    <Run Id="synth_1" Type="Ft3:Synth" SrcSet="sources_1" Part="xc7z100ffg900-2" ConstrsSet="constrs_1" Description="Vivado Synthesis Defaults" AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" State="current" IncludeInArchive="true" IsChild="false" AutoIncrementalDir="$PSRCDIR/utils_1/imports/synth_1">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Vivado Synthesis Defaults" Flow="Vivado Synthesis 2021">
          <Desc>Vivado Synthesis Defaults</Desc>
        </StratHandle>
        <Step Id="synth_design"/>
      </Strategy>
      <ReportStrategy Name="Vivado Synthesis Default Reports" Flow="Vivado Synthesis 2021"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
      <RQSFiles/>
    </Run>
    <Run Id="impl_1" Type="Ft2:EntireDesign" Part="xc7z100ffg900-2" ConstrsSet="constrs_1" Description="Default settings for Implementation." AutoIncrementalCheckpoint="false" WriteIncrSynthDcp="false" State="current" SynthRun="synth_1" IncludeInArchive="true" IsChild="false" GenFullBitstream="true" AutoIncrementalDir="$PSRCDIR/utils_1/imports/impl_1">
      <Strategy Version="1" Minor="2">
        <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2021">
          <Desc>Default settings for Implementation.</Desc>
        </StratHandle>
        <Step Id="init_design"/>
        <Step Id="opt_design"/>
        <Step Id="power_opt_design"/>
        <Step Id="place_design"/>
        <Step Id="post_place_power_opt_design"/>
        <Step Id="phys_opt_design"/>
        <Step Id="route_design"/>
        <Step Id="post_route_phys_opt_design"/>
        <Step Id="write_bitstream"/>
      </Strategy>
      <ReportStrategy Name="Vivado Implementation Default Reports" Flow="Vivado Implementation 2021"/>
      <Report Name="ROUTE_DESIGN.REPORT_METHODOLOGY" Enabled="1"/>
      <RQSFiles/>
    </Run>
  </Runs>
  <Board/>
  <DashboardSummary Version="1" Minor="0">
    <Dashboards>
      <Dashboard Name="default_dashboard">
        <Gadgets>
          <Gadget Name="drc_1" Type="drc" Version="1" Row="2" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_1#impl_1_route_report_drc_0 "/>
          </Gadget>
          <Gadget Name="methodology_1" Type="methodology" Version="1" Row="2" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_1#impl_1_route_report_methodology_0 "/>
          </Gadget>
          <Gadget Name="power_1" Type="power" Version="1" Row="1" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_1#impl_1_route_report_power_0 "/>
          </Gadget>
          <Gadget Name="timing_1" Type="timing" Version="1" Row="0" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_1#impl_1_route_report_timing_summary_0 "/>
          </Gadget>
          <Gadget Name="utilization_1" Type="utilization" Version="1" Row="0" Column="0">
            <GadgetParam Name="REPORTS" Type="string_list" Value="synth_1#synth_1_synth_report_utilization_0 "/>
            <GadgetParam Name="RUN.STEP" Type="string" Value="synth_design"/>
            <GadgetParam Name="RUN.TYPE" Type="string" Value="synthesis"/>
          </Gadget>
          <Gadget Name="utilization_2" Type="utilization" Version="1" Row="1" Column="1">
            <GadgetParam Name="REPORTS" Type="string_list" Value="impl_1#impl_1_place_report_utilization_0 "/>
          </Gadget>
        </Gadgets>
      </Dashboard>
      <CurrentDashboard>default_dashboard</CurrentDashboard>
    </Dashboards>
  </DashboardSummary>
</Project>
